#!/usr/bin/env python3
"""
Notion数据库重复标题检测脚本
通过Keyboard Maestro触发，搜索Notion数据库并找出重复的标题
"""

import requests
import json
from collections import defaultdict
import sys
import os

# 导入配置
try:
    from config import NOTION_API_TOKEN, DATABASE_ID, NOTION_VERSION
except ImportError:
    # 如果没有配置文件，使用默认值
    NOTION_API_TOKEN = "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"
    DATABASE_ID = "23895d7611248123a7fcf8b25d2d7998"
    NOTION_VERSION = "2022-06-28"

# API请求头
HEADERS = {
    "Authorization": f"Bearer {NOTION_API_TOKEN}",
    "Content-Type": "application/json",
    "Notion-Version": NOTION_VERSION
}

def get_all_database_entries():
    """
    获取数据库中的所有条目
    """
    url = f"https://api.notion.com/v1/databases/{DATABASE_ID}/query"
    
    all_entries = []
    has_more = True
    start_cursor = None
    
    while has_more:
        payload = {
            "page_size": 100  # 每次最多获取100条记录
        }
        
        if start_cursor:
            payload["start_cursor"] = start_cursor
        
        try:
            response = requests.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            
            data = response.json()
            all_entries.extend(data.get("results", []))
            
            has_more = data.get("has_more", False)
            start_cursor = data.get("next_cursor")
            
        except requests.exceptions.RequestException as e:
            print(f"API请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
    
    return all_entries

def extract_title_from_page(page):
    """
    从页面对象中提取标题
    """
    try:
        # 尝试从properties中获取标题
        properties = page.get("properties", {})
        
        # 查找标题属性（通常是title类型）
        for prop_name, prop_value in properties.items():
            if prop_value.get("type") == "title":
                title_array = prop_value.get("title", [])
                if title_array:
                    return "".join([text.get("plain_text", "") for text in title_array])
        
        # 如果没有找到title属性，尝试使用页面的默认标题
        if "properties" in page and "Name" in page["properties"]:
            name_prop = page["properties"]["Name"]
            if name_prop.get("type") == "title":
                title_array = name_prop.get("title", [])
                if title_array:
                    return "".join([text.get("plain_text", "") for text in title_array])
        
        # 最后尝试从页面的其他可能的标题字段获取
        return page.get("url", "无标题")
        
    except Exception as e:
        print(f"提取标题时出错: {e}")
        return "提取失败"

def find_duplicates(entries):
    """
    查找重复的标题
    """
    title_to_pages = defaultdict(list)
    
    for entry in entries:
        title = extract_title_from_page(entry)
        if title and title.strip():  # 确保标题不为空
            title_to_pages[title.strip()].append({
                "id": entry.get("id"),
                "url": entry.get("url"),
                "created_time": entry.get("created_time"),
                "last_edited_time": entry.get("last_edited_time")
            })
    
    # 找出重复的标题
    duplicates = {title: pages for title, pages in title_to_pages.items() if len(pages) > 1}
    
    return duplicates

def format_output(duplicates):
    """
    格式化输出结果
    """
    if not duplicates:
        return "✅ 没有发现重复的标题！"
    
    output = []
    output.append(f"🔍 发现 {len(duplicates)} 个重复的标题：\n")
    
    for title, pages in duplicates.items():
        output.append(f"📝 标题: '{title}' (重复 {len(pages)} 次)")
        for i, page in enumerate(pages, 1):
            output.append(f"   {i}. ID: {page['id']}")
            output.append(f"      URL: {page['url']}")
            output.append(f"      创建时间: {page['created_time']}")
            output.append(f"      最后编辑: {page['last_edited_time']}")
        output.append("")  # 空行分隔
    
    return "\n".join(output)

def main():
    """
    主函数
    """
    print("🚀 开始搜索Notion数据库...")
    
    # 获取所有数据库条目
    entries = get_all_database_entries()
    
    if entries is None:
        print("❌ 获取数据库条目失败")
        sys.exit(1)
    
    print(f"📊 总共找到 {len(entries)} 个条目")
    
    # 查找重复项
    duplicates = find_duplicates(entries)
    
    # 输出结果
    result = format_output(duplicates)
    print(result)
    
    # 可选：将结果保存到文件
    output_file = "notion_duplicates_report.txt"
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(result)
        print(f"\n📄 结果已保存到: {output_file}")
    except Exception as e:
        print(f"⚠️  保存文件时出错: {e}")

if __name__ == "__main__":
    main()
