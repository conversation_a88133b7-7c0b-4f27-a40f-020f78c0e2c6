#!/usr/bin/env python3
"""
Notion数据库批量创建工具
从剪贴板读取标题列表，检查重复并创建新条目
"""

import requests
import json
import sys
import os
from collections import defaultdict

# 导入配置
try:
    from config import NOTION_API_TOKEN, DATABASE_ID, NOTION_VERSION
except ImportError:
    NOTION_API_TOKEN = "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"
    DATABASE_ID = "23895d7611248123a7fcf8b25d2d7998"
    NOTION_VERSION = "2022-06-28"

# API请求头
HEADERS = {
    "Authorization": f"Bearer {NOTION_API_TOKEN}",
    "Content-Type": "application/json",
    "Notion-Version": NOTION_VERSION
}

def get_content_from_km_variable():
    """从Keyboard Maestro变量获取内容"""
    try:
        # 从环境变量获取CurrentLine
        content = os.environ.get('KMVAR_CurrentLine')
        if content:
            return content
        else:
            print("❌ 未找到Keyboard Maestro变量 CurrentLine")
            return None
    except Exception as e:
        print(f"❌ 读取Keyboard Maestro变量时出错: {e}")
        return None

def get_all_existing_titles():
    """获取数据库中所有现有标题"""
    url = f"https://api.notion.com/v1/databases/{DATABASE_ID}/query"
    
    all_titles = set()
    has_more = True
    start_cursor = None
    
    print("🔍 正在获取现有标题...")
    
    while has_more:
        payload = {"page_size": 100}
        if start_cursor:
            payload["start_cursor"] = start_cursor
        
        try:
            response = requests.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            
            data = response.json()
            entries = data.get("results", [])
            
            for entry in entries:
                title = extract_title_from_page(entry)
                if title and title.strip():
                    all_titles.add(title.strip())
            
            has_more = data.get("has_more", False)
            start_cursor = data.get("next_cursor")
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return None
    
    return all_titles

def extract_title_from_page(page):
    """从页面对象中提取标题"""
    try:
        properties = page.get("properties", {})
        
        # 查找标题属性
        for prop_name, prop_value in properties.items():
            if prop_value.get("type") == "title":
                title_array = prop_value.get("title", [])
                if title_array:
                    return "".join([text.get("plain_text", "") for text in title_array])
        
        return "无标题"
        
    except Exception as e:
        return "提取失败"

def get_database_properties():
    """获取数据库的属性结构"""
    url = f"https://api.notion.com/v1/databases/{DATABASE_ID}"
    
    try:
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        
        data = response.json()
        properties = data.get("properties", {})
        
        # 找到标题属性的名称
        title_property_name = None
        for prop_name, prop_value in properties.items():
            if prop_value.get("type") == "title":
                title_property_name = prop_name
                break
        
        return title_property_name, properties
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取数据库属性错误: {e}")
        return None, None

def create_page_in_database(title, title_property_name):
    """在数据库中创建新页面"""
    url = "https://api.notion.com/v1/pages"
    
    payload = {
        "parent": {
            "database_id": DATABASE_ID
        },
        "properties": {
            title_property_name: {
                "title": [
                    {
                        "text": {
                            "content": title
                        }
                    }
                ]
            }
        }
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=payload)
        response.raise_for_status()
        
        data = response.json()
        return data.get("url"), data.get("id")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建页面错误: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"   响应内容: {e.response.text}")
        return None, None

def parse_titles_from_text(text):
    """从文本中解析标题列表"""
    if not text:
        return []
    
    # 按行分割并清理
    lines = text.strip().split('\n')
    titles = []
    
    for line in lines:
        title = line.strip()
        if title:  # 忽略空行
            titles.append(title)
    
    return titles

def main():
    """主函数"""
    print("📋 Notion批量创建工具")
    print("=" * 50)
    
    # 1. 从Keyboard Maestro变量获取内容
    print("📥 正在读取Keyboard Maestro变量 CurrentLine...")
    content = get_content_from_km_variable()

    if not content:
        print("❌ 无法获取内容，请确保在Keyboard Maestro中设置了CurrentLine变量")
        sys.exit(1)

    print(f"✅ 成功读取内容 ({len(content)} 字符)")
    
    # 2. 解析标题列表
    titles_to_create = parse_titles_from_text(content)
    
    if not titles_to_create:
        print("❌ 没有找到有效的标题")
        sys.exit(1)
    
    print(f"📝 解析到 {len(titles_to_create)} 个标题:")
    for i, title in enumerate(titles_to_create, 1):
        print(f"   {i}. {title}")
    print()
    
    # 3. 获取现有标题
    existing_titles = get_all_existing_titles()
    
    if existing_titles is None:
        print("❌ 无法获取现有标题")
        sys.exit(1)
    
    print(f"📊 数据库中现有 {len(existing_titles)} 个标题")
    
    # 4. 获取数据库属性结构
    title_property_name, properties = get_database_properties()
    
    if not title_property_name:
        print("❌ 无法获取数据库标题属性")
        sys.exit(1)
    
    print(f"🏗️  标题属性名称: {title_property_name}")
    print()
    
    # 5. 处理每个标题
    print("🚀 开始处理标题...")
    print("=" * 50)
    
    created_count = 0
    skipped_count = 0
    failed_count = 0
    
    for i, title in enumerate(titles_to_create, 1):
        print(f"[{i}/{len(titles_to_create)}] 处理: '{title}'")
        
        # 检查是否已存在
        if title in existing_titles:
            print(f"   ⚠️  已存在，跳过创建")
            skipped_count += 1
        else:
            # 创建新条目
            print(f"   🔨 正在创建...")
            page_url, page_id = create_page_in_database(title, title_property_name)
            
            if page_url:
                print(f"   ✅ 创建成功: {page_url}")
                created_count += 1
                # 添加到现有标题集合中，避免后续重复
                existing_titles.add(title)
            else:
                print(f"   ❌ 创建失败")
                failed_count += 1
        
        print()
    
    # 6. 输出统计结果
    print("📊 处理完成统计:")
    print("=" * 50)
    print(f"✅ 成功创建: {created_count} 个")
    print(f"⚠️  跳过重复: {skipped_count} 个")
    print(f"❌ 创建失败: {failed_count} 个")
    print(f"📝 总计处理: {len(titles_to_create)} 个")
    
    if created_count > 0:
        print(f"\n🎉 成功创建了 {created_count} 个新条目！")

if __name__ == "__main__":
    main()
