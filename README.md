# Notion数据库重复标题检测工具

这个工具可以帮你检测Notion数据库中是否存在重复的标题，并通过Keyboard Maestro触发。

## 文件说明

- `notion_duplicate_finder.py` - 主要的Python脚本
- `config.py` - 配置文件，包含API密钥和数据库ID
- `run_notion_check.sh` - Bash脚本，用于Keyboard Maestro触发
- `README.md` - 说明文档

## 安装和配置

### 1. 确保Python环境

确保你的Mac上安装了Python3：
```bash
python3 --version
```

### 2. 安装依赖

脚本会自动安装`requests`库，或者你可以手动安装：
```bash
pip3 install requests
```

### 3. 配置API信息

编辑`config.py`文件，确认以下信息正确：
- `NOTION_API_TOKEN`: 你的Notion API密钥
- `DATABASE_ID`: 你的数据库ID

## 使用方法

### 方法1: 直接运行Python脚本
```bash
python3 notion_duplicate_finder.py
```

### 方法2: 通过Bash脚本运行
```bash
chmod +x run_notion_check.sh
./run_notion_check.sh
```

### 方法3: 通过Keyboard Maestro触发

1. 打开Keyboard Maestro
2. 创建新的宏
3. 设置触发条件（如快捷键）
4. 添加"Execute Shell Script"动作
5. 在脚本内容中输入：
   ```bash
   cd "脚本所在的完整路径"
   ./run_notion_check.sh
   ```

## 输出结果

脚本会：
1. 在终端显示检测结果
2. 将结果保存到`notion_duplicates_report.txt`文件

输出格式示例：
```
🔍 发现 2 个重复的标题：

📝 标题: '项目计划' (重复 3 次)
   1. ID: abc123...
      URL: https://notion.so/...
      创建时间: 2024-01-01T10:00:00.000Z
      最后编辑: 2024-01-02T15:30:00.000Z
   
   2. ID: def456...
      URL: https://notion.so/...
      创建时间: 2024-01-03T09:15:00.000Z
      最后编辑: 2024-01-03T09:15:00.000Z
```

## 注意事项

1. 确保你的Notion API密钥有访问指定数据库的权限
2. 脚本会获取数据库中的所有条目，如果数据量很大可能需要一些时间
3. 重复检测基于标题的完全匹配（忽略前后空格）
4. 结果文件会在每次运行时被覆盖

## 故障排除

### 常见错误

1. **API请求错误**: 检查网络连接和API密钥
2. **权限错误**: 确保API密钥有访问数据库的权限
3. **Python模块错误**: 运行`pip3 install requests`安装依赖

### 获取数据库ID

从Notion数据库URL中提取ID：
```
https://www.notion.so/23895d7611248123a7fcf8b25d2d7998?v=...
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    这部分就是数据库ID
```

## 自定义

你可以修改`notion_duplicate_finder.py`来：
- 改变输出格式
- 添加更多的检测条件
- 导出到不同格式（CSV、JSON等）
- 添加自动删除重复项的功能（谨慎使用）
