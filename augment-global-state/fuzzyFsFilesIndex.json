{"/Volumes/music/虾米网易云/纯音乐/@eaDir/S.E.N.S. (神思者) - Magic Warriors [mqms2].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/S.E.N.S. (神思者) - Magic Warriors [mqms2].mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲二十二.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲二十二.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲五.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲五.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲四.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲四.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 街道的寂寞.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 街道的寂寞.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 被时光移动的城市.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 被时光移动的城市.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 雨葵.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 雨葵.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Greak meditation.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Greak meditation.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Indian dreams.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Indian dreams.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Machu piddhu.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - <PERSON><PERSON> piddhu.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Pastel reflections 温柔再现.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Pastel reflections 温柔再现.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Whistle of wind.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Whistle of wind.mp3@SynoResource"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/霞光_吴金黛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/霞光_吴金黛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/雲流れ_みかん箱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/雲流れ_みかん箱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/雨空_α·Pav.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/雨空_α·Pav.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/雨夜钢琴_林志美.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/雨夜钢琴_林志美.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/雨丝情愁_王闻.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/雨丝情愁_王闻.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/銀の龍の背に乗って_中島みゆき.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/銀の龍の背に乗って_中島みゆき.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/远方的寂静_林海.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/远方的寂静_林海.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/远TONE音 - 茜云.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/远TONE音 - 茜云.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/辛德勒的名单CD原声.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/辛德勒的名单CD原声.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/赵海洋 - 童年的回忆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/赵海洋 - 童年的回忆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/赵海洋 - 夜空的寂静.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/赵海洋 - 夜空的寂静.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/萤火虫之舞_萤火虫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/萤火虫之舞_萤火虫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/羽毛田丈史 - 里山.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/羽毛田丈史 - 里山.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - 随想曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - 随想曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - 秋水悠悠 古琴、箫、巴乌.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - 秋水悠悠 古琴、箫、巴乌.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - 湛蓝的思绪.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - 湛蓝的思绪.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - 月光小夜曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - 月光小夜曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - 小刀会序曲 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - 小刀会序曲 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Whistle of wind.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Whistle of wind.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Whistle of wind [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Whistle of wind [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - The Waiting Game [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - The Waiting Game [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Pastel reflections 温柔再现.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Pastel reflections 温柔再现.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Machu piddhu.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - <PERSON><PERSON> piddhu.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Indian dreams.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Indian dreams.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/群星 - Greak meditation.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/群星 - Greak meditation.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/縁-えにし-_S.E.N.S..mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/縁-えにし-_S.E.N.S..mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/竹舞_邵容.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/竹舞_邵容.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 雨葵.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 雨葵.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 被时光移动的城市.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 被时光移动的城市.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 街道的寂寞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 街道的寂寞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲四.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲四.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲五.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲五.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 夜的钢琴曲二十二.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 夜的钢琴曲二十二.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/石进 - 一个人的时光.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/石进 - 一个人的时光.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/瑞鸣唱片 - 卡农.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/瑞鸣唱片 - 卡农.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林狂想曲_群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林狂想曲_群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 阿兰尼兹之恋 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 阿兰尼兹之恋 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 重归苏连坡 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 重归苏连坡 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 远航 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 远航 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 蓝色的爱 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 蓝色的爱 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 美丽的梦仙 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 美丽的梦仙 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 绿袖子 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 绿袖子 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 琴韵星空 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 琴韵星空 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 琴泰岬 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 琴泰岬 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 爱的无涯 [mqms2].mp3/SYNOAUDIO_SONG_RATING": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 爱的无涯 [mqms2].mp3/SYNOAUDIO_SONG_RATING"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 爱的无涯 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 爱的无涯 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 海 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 海 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 月亮河 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 月亮河 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 昨天的我 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 昨天的我 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 情人的婚礼 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 情人的婚礼 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 寂静 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 寂静 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 威尼斯之游 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 威尼斯之游 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 伦敦德里小调 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 伦敦德里小调 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 人鬼情未了 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 人鬼情未了 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - 七海 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - 七海 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - Summer Diary [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - Summer Diary [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/森林物语 - Rainbow [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/森林物语 - Rainbow [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/桜花抄_天門.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/桜花抄_天門.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/有里知花 - 涙の物语.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/有里知花 - 涙の物语.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/月光边境_林海.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/月光边境_林海.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/月光小夜曲_群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/月光小夜曲_群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/月光女神 (人声)_群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/月光女神 (人声)_群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/月光の雲海_Joe Hisaishi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/月光の雲海_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/昼下がりの憂鬱_みかん箱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/昼下がりの憂鬱_みかん箱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/新加坡淨宗學會 - 清尘雅琴 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/新加坡淨宗學會 - 清尘雅琴 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/新加坡淨宗學會 - 一声佛号一声心 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/新加坡淨宗學會 - 一声佛号一声心 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/拾起童年 _萤火虫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/拾起童年 _萤火虫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/心を開いて  _羽田裕美.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/心を開いて  _羽田裕美.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/张宇桦 - 优美的小调.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/张宇桦 - 优美的小调.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/幽游白书.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/幽游白书.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/幸せ_小林幸子.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/幸せ_小林幸子.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/山本サヤカ - 雪山の白い使い.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/山本サヤカ - 雪山の白い使い.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/尤里沙图诺夫 - Не говори мне ничего.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/尤里沙图诺夫 - Не говори мне ничего.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/少女のうた_高梨康治.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/少女のうた_高梨康治.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/媛星の静けさ_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/媛星の静けさ_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/女子十二乐坊 - 金蛇狂舞 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/女子十二乐坊 - 金蛇狂舞 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/夏川里美 - 涙そうそう.flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/夏川里美 - 涙そうそう.flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/夏天的味道_海雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/夏天的味道_海雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/夏天来了_萤火虫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/夏天来了_萤火虫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/喜多郎 - Kiaro Matsurl.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/喜多郎 - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/喜多郎 (Kitaro) - “The Soong sisters”main title [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/喜多郎 (Kit<PERSON>) - “The Soong sisters”main title [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/喜多郎 (Kitaro) - The Inmost Feeling Ripples [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/喜多郎 (Kit<PERSON>) - The Inmost Feeling Ripples [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/吴金黛 - 森林狂想曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/吴金黛 - 森林狂想曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/兄弟~亮と瑾_横山菁児.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/兄弟~亮と瑾_横山菁児.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/倖田來未 - COME WITH ME(Original Mix) - remix.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/倖田來未 - COME WITH ME(Original Mix) - remix.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/佐藤康夫 - 夜明.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/佐藤康夫 - 夜明.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/中村由利子 - “冬のソナタ”~初めから今まで.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/中村由利子 - “冬のソナタ”~初めから今まで.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/mickey - 云端的天使.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/mickey - 云端的天使.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/main theme ending_川井憲次.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/main theme ending_川井憲次.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/ignited ~piano version_佐橋俊彦.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/ignited ~piano version_佐橋俊彦.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/a_hisa - ほたる火.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/a_hisa - ほたる火.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - 贝多芬第8号钢琴奏鸣曲悲伤的第三乐章.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - 贝多芬第8号钢琴奏鸣曲悲伤的第三乐章.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - 春之声圆舞曲 小约翰.施特劳斯.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - 春之声圆舞曲 小约翰.施特劳斯.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - 匈牙利圆舞曲 勃拉姆斯.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - 匈牙利圆舞曲 勃拉姆斯.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - 克罗地亚第二狂想曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - 克罗地亚第二狂想曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - Shenandoah [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - <PERSON><PERSON><PERSON>h [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - Ready for times get better 期待好时光 gest always.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - Ready for times get better 期待好时光 gest always.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Various Artists - Elgar – Salut D’amour Op. 12 艾尔加：爱的礼赞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Various Artists - <PERSON><PERSON> – Salut D’amour Op. 12 艾尔加：爱的礼赞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Variations on the Kanon by Pac_George Winston.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Variations on the Kanon by <PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Vanishing Blue_Tigerforest.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Vanishing Blue_Tigerforest.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Vangelis - Conquest Of Paradise [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> - Conquest Of Paradise [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Valentin - A Little Story.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Valentin - A Little Story.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Vain_大森俊之.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Vain_大森俊之.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/VAMPIRE BALL_羽毛田丈史.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/VAMPIRE BALL_羽毛田丈史.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/V.A. - Sweet Days.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/V.A. - Sweet Days.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/V.A. - Greatest Love of All [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/V.A. - Greatest Love of All [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/V.A. - Always With Me(木吉他).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/V.A. - Always With Me(木吉他).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Uptown Funk_Mark Ronson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Uptown Funk_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Up in the Sky_松居和.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Up in the Sky_松居和.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Twins - 女人味 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Twins - 女人味 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Twins - 倾心 (Live) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Twins - 倾心 (Live) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Twilight's Embrace_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Twilight's Em<PERSON>ce_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Turning Page (Instrumental)_Sleeping at Last.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Turning Page (Instrumental)_Sleeping at Last.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Truong Quynh Anh - Don Coi (Instrumental).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> (Instrumental).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Trouble Is A Friend_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Trouble Is A Friend_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Traveling Light_Joel Hanson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Traveling Light_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Transylvanian Lullaby_David Wright.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON>n <PERSON><PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Top Of The World_Naomi & Goro.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Top Of The World_Naomi & Goro.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/To Zanarkand -FFX-_植松伸夫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/To Zanarkand -FFX-_植松伸夫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Through the Arbor_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Through the Arbor_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Through Your Eyes_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Through Your Eyes_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Threads of Light_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Threads of <PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Theme For The Oddmory Philosop_The Seven Mile Journey.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Theme For The Oddmory Philosop_The Seven Mile Journey.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Whole Truth_Carlos Cipa.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Whole Truth_Carlos Cipa.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Way We Were_David Davidson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Way We Were_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Voyage_The Mountaineering Club Orchestra.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Voyage_The Mountaineering Club Orchestra.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Treasure_Bernward Koch.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Treasure_Bernward Koch.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Touch of Love_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Touch of Love_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Sun In The Stream _Giovanni.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Sun In The Stream _Giovanni.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Silver Branch_Dagda.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Silver Branch_Dagda.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Rivers That Run Beneath Th_The Calm Blue Sea.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Rivers That Run Beneath Th_The Calm Blue Sea.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Promise_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Promise_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/The Loveliest Flower in My Gar_Gandalf.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/The Loveliest Flower in My Gar_Gandalf.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/TV Ending Theme_吉田潔.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/TV Ending Theme_吉田潔.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/TLC - I Miss You So Much.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/TLC - I Miss You So Much.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Summer'S Love_Kenio Fuke.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Summer'S Love_Kenio Fuke.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Spring_张一益.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Spring_张一益.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sparrowed Chimes - The Day We Died [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sparrowed Chimes - The Day We Died [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Soong Sisters_Kitaro.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Soong Sisters_Kitaro.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Song for Lovers_The Dø.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Song for Lovers_The Dø.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sometime_Bernward Koch.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sometime_<PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Solitude _坂本龍一.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Solitude _坂本龍一.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sky Road Memories_Back to Earth.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sky Road Memories_Back to Earth.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Silk Road _Kitaro.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Silk Road _Kitaro.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Serenade, for piano (after Sch_Michael Dulin.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON>, for piano (after <PERSON><PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sensitive Heart - 竹取飛翔 ～ Lunatic Princess.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sensitive Heart - 竹取飛翔 ～ Lunatic Princess.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sensitive Heart - 琥珀色の海へ.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sensitive Heart - 琥珀色の海へ.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sensitive Heart - 予感.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sensitive Heart - 予感.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Secret Garden - Pastorale (田园曲) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Secret Garden - Pastorale (田园曲) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Sea of Dreams_2002.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Sea of Dreams_2002.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/SMOOTH J - 旅人~第1章~.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/SMOOTH J - 旅人~第1章~.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/S.E.N.S. (神思者) - Magic Warriors [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/S.E.N.S. (神思者) - Magic Warriors [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Rising Girl (Radio Version)_Lovestoned.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Rising Girl (Radio Version)_Lovestoned.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Richard Clayderman - 绿袖子 (绿袖子) [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - 绿袖子 (绿袖子) [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Richard Clayderman - Greensleeves (绿袖子) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - <PERSON>leeves (绿袖子) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Richard Clayderman - Ballade Pour Adeline [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - <PERSON>e <PERSON> [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Retim - Yadikar [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> - <PERSON><PERSON> [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Remember_김윤.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Remember_김윤.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Rejuvenescence_Luna Sea.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Rejuvenescence_Luna Sea.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Refrain_Anan Ryoko.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Refrain_Anan Ryoko.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Raphaël Beau - Larrons En Foire.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> En Foire.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Rain_Circadian Eyes.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Rain_Circadian Eyes.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pure Music - 小夜曲 [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pure Music - 小夜曲 [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pure Music - 啤酒桶波尔卡 [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pure Music - 啤酒桶波尔卡 [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pure Music - 十面埋伏 [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pure Music - 十面埋伏 [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Promises Don't Come Easy_群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Promises Don't Come Easy_群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Primi passi_Fabrizio Paterlini.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Primi passi_<PERSON><PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Port Blue - Arrival at Sydney Harbour.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Port Blue - Arrival at Sydney Harbour.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pole_Djelem.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pole_Djelem.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Places That Belong to You_Danny Wright.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Places That Belong to <PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pedalo_The Heart Strings.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pedalo_The Heart Strings.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Paul Mauriat - Those Were The Days [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - Those Were The Days [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Paul Mauriat - The Piano On The Wave [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - The Piano On The Wave [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Paul Mauriat - Penelope [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - <PERSON> [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Paul Mauriat - Love is Blue (爱是忧郁) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - Love is Blue (爱是忧郁) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pastorale_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pastorale_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pastel Reflections_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Pastel Reflections_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Papillon_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Papillon_Secret Garden.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pan's Dream_Giovanni.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON>'s Dream_Giovanni.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pachelbel's Canon (A Variation_Hilary Stagg.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON>'s Canon (A Variation_Hilary Stagg.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Pachebel's Canon in D_Danny Wright.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON>'s Canon in <PERSON>_Danny Wright.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Out of the Darkness into the L_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Out of the Darkness into the L_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Otokaze - 夏恋.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Otokaze - 夏恋.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Opus (Music Box) _ルルティア.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Opus (Music Box) _ルルティア.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Opus #12_Dustin O'Halloran.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Opus #12_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/My Memory ~「冬のソナタ」より_広橋真紀子.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/My Memory ~「冬のソナタ」より_広橋真紀子.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/My Memory (Piano Ver.)_박정원.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/My Memory (Piano Ver.)_박정원.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Mr.Cali - 克罗地亚狂想曲(Cali Remix).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Mr.<PERSON>i - 克罗地亚狂想曲(Cali Remix).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/M07_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/M07_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/M01_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/M01_梶浦由記.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Luv Letter_DJ OKAWARI.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Luv Letter_DJ OKAWARI.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Luna_Bandari.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Luna_Bandari.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Lee Ryan - Army Of Lovers.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - Army Of Lovers.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Leaves in the Wind_Isaac Shepard.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Leaves in the Wind_Isaac Shepard.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Leaf - 言葉にできない想い.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Leaf - 言葉にできない想い.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Lawrence - 涟漪.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Lawrence - 涟漪.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Laura Story - Grace.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Laura Story - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Laura Pausini - It's Not Good-Bye.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - It's Not Good-Bye.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Last Train Home ～still far_麗美.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Last Train Home ～still far_麗美.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Land of Forever_2002.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Land of Forever_2002.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Key Sounds Label - 潮鳴り.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Key Sounds Label - 潮鳴り.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Kenny G - 回家 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Kenny G - 回家 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/K. Williams - 菊次郎的夏天.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON> - 菊次郎的夏天.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/K. Williams - 忧伤还是快乐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON> - 忧伤还是快乐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/K. Williams - 天空之城.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON> - 天空之城.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Juwita Suwito - Breathe Again.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> - Breathe Again.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Justin Timberlake Carey Mulligan Stark Sands - Five Hundred Miles.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON>gan <PERSON> - Five Hundred Miles.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Johann Sebastian Bach - 巴赫G骇之歌 [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - 巴赫G骇之歌 [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Instrumental - Sailing [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Instrumental - Sailing [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Instrumental - Bilitis [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Instrumental - Bilitis [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/In a Protected Cove_Dan Gibson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/In a Protected Cove_Dan Gibson.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/In a Notebook_Goldmund.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/In a Notebook_Goldmund.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/In My Life_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/In My Life_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Igor Krutoi - Sad Angel (悲伤的天使) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - <PERSON> (悲伤的天使) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Hemingbough.... A Prelude_Robert Haig Coxon.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON><PERSON>.... A Prelude_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 高胡与乐队《平湖秋月》 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 高胡与乐队《平湖秋月》 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 旱天雷 (Thunder in the Dry Season) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 旱天雷 (Thunder in the Dry Season) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 彩云追月 The Clouds Chasing the Moon [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Guangdong Music Troupe Orchestra - 彩云追月 The Clouds Chasing the Moon [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Groove Coverage - I Need You Vs. I Need You.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Groove Coverage - I Need You Vs. I Need You.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Groove Coverage - 7 Years And 50 Days.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Groove Coverage - 7 Years And 50 Days.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Greensleeves (Remix)_笨鸟暂存.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Greensleeves (Remix)_笨鸟暂存.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Foxtail-Grass Studio - 歳月-雲流れ-.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Foxtail-Grass Studio - 歳月-雲流れ-.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Forgiving Winter_Robin Spielberg.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Forgiving Winter_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ethno Music Project - The Golden Harp.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ethno Music Project - The Golden Harp.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ethno Music Project - Crystal Bal.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ethno Music Project - Crystal Bal.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ethno Music Project - Childhood Memory.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ethno Music Project - Childhood Memory.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ethno Music Project - Celtic Waltz.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ethno Music Project - Celtic Waltz.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Dan Gibson - Arrival Of Spring [mqms].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Dan <PERSON> - Arrival Of Spring [mqms].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Damian Luca - China Roses.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> - <PERSON> Roses.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Confessions in the Moonlight_Joe Hisaishi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Confessions in the Moonlight_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Coming Home_Robert Haig Coxon.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Coming Home_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Childhood Memory_熱田公紀.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Childhood Memory_熱田公紀.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Canon_Gustavo Montesano.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Canon in D_Tom Barabas.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Canon in D_Tom Barabas.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Baptism_Paul Cardall.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Baptism_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - 雪之梦 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - 雪之梦 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - 爱尔兰摇篮曲 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - 爱尔兰摇篮曲 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Woodland Night (森林中的一夜) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Woodland Night (森林中的一夜) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - White Sand Of Barbados [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - White Sand Of Barbados [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Whistle Of The Wind (风的呢喃) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Whistle Of The Wind (风的呢喃) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Luna (月亮) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON> - Luna (月亮) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Irish Lullaby (爱尔兰摇篮曲) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Irish Lullaby (爱尔兰摇篮曲) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Endless Horizon (无垠水平线) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Endless Horizon (无垠水平线) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Endles Horizon (无垠地平线) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Endles Horizon (无垠地平线) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Children's Eyes (孩子的眼睛) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Children's Eyes (孩子的眼睛) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Childhood Memory (童年的回忆) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Childhood Memory (童年的回忆) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - Archeo Piima Ancient Poem [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - Archeo Piima Ancient Poem [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Bandari - A Woodland Night (森林中的一夜) [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Bandari - A Woodland Night (森林中的一夜) [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ballas Hough Band - Underwater.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ballas Hough Band - Underwater.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ballade pour Adeline(Paul de S_Richard Clayderman.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ballade pour Adeline(<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ballade Pour Adeline_Bandari.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ballade Pour Adeline_Bandari.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Atchorite - Kimi Wo Omou Melodi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Atchorite - <PERSON><PERSON>o <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ashthon Jones - When You Tell Me That You Love Me.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON><PERSON><PERSON> - When You Tell Me That You Love Me.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Arrival Of The Birds_The Cinematic Orchestra.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Arrival Of The Birds_The Cinematic Orchestra.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Arrival Of Spring_Dan Gibson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Arrival Of Spring_Dan Gibson.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Aron Bergen - 5th of August.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Aron Bergen - 5th of August.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Angel of Hope_Omar Akram.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/<PERSON> of Hope_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Ancient Rainforest_Steve Middleton.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Ancient Rainforest_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/After the Rain_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/After the Rain_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/Above the Clouds_Kevin Kern.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/Above the Clouds_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/AGTM 木吉他俱乐部 - 天空之城 [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/AGTM 木吉他俱乐部 - 天空之城 [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/A Time For Us_David Davidson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/A Time For Us_David <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/A Place in the Sun_Gandalf.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/A Place in the Sun_Gandalf.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/A Dead Leaf Dance_As the Stars Fall.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/A Dead Leaf Dance_As the Stars Fall.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/纯音乐/@eaDir/A Christmas Wedding_Fiona Joy Hawkins.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "纯音乐/@eaDir/A Christmas Wedding_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/001.光良-童话.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/001.光良-童话.lrc"}, "/Volumes/music/虾米网易云/流行/004.游鸿明-下沙.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/004.游鸿明-下沙.lrc"}, "/Volumes/music/虾米网易云/流行/005.周传雄-黄昏.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/005.周传雄-黄昏.lrc"}, "/Volumes/music/虾米网易云/流行/006.陈琳-你的柔情我永远不懂.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/006.陈琳-你的柔情我永远不懂.lrc"}, "/Volumes/music/虾米网易云/流行/009.巫启贤-太傻.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/009.巫启贤-太傻.lrc"}, "/Volumes/music/虾米网易云/流行/0099-北京北京-汪峰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/0099-北京北京-汪峰.lrc"}, "/Volumes/music/虾米网易云/流行/01-01.小李飞刀-罗文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/01-01.小李飞刀-罗文.lrc"}, "/Volumes/music/虾米网易云/流行/010.张韶涵-阿刁.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/010.张韶涵-阿刁.lrc"}, "/Volumes/music/虾米网易云/流行/011.满文军-懂你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/011.满文军-懂你.lrc"}, "/Volumes/music/虾米网易云/流行/012.张国荣-风继续吹.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/012.张国荣-风继续吹.lrc"}, "/Volumes/music/虾米网易云/流行/015.腾格尔-天堂.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/015.腾格尔-天堂.lrc"}, "/Volumes/music/虾米网易云/流行/016.周深-大鱼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/016.周深-大鱼.lrc"}, "/Volumes/music/虾米网易云/流行/017.张镐哲-好男人.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/017.张镐哲-好男人.lrc"}, "/Volumes/music/虾米网易云/流行/019.李智楠-红色石头.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/019.李智楠-红色石头.lrc"}, "/Volumes/music/虾米网易云/流行/02-01.天蚕变-关正杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/02-01.天蚕变-关正杰.lrc"}, "/Volumes/music/虾米网易云/流行/02-07.眼泪为你流-陈百强.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/02-07.眼泪为你流-陈百强.lrc"}, "/Volumes/music/虾米网易云/流行/021.王馨平-别问我是谁.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/021.王馨平-别问我是谁.lrc"}, "/Volumes/music/虾米网易云/流行/022.任贤齐-流着泪的你的脸.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/022.任贤齐-流着泪的你的脸.lrc"}, "/Volumes/music/虾米网易云/流行/025.陈星-流浪歌.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/025.陈星-流浪歌.lrc"}, "/Volumes/music/虾米网易云/流行/0278-丁香花.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/0278-丁香花.lrc"}, "/Volumes/music/虾米网易云/流行/028.苏永康-爱一个人好难.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/028.苏永康-爱一个人好难.lrc"}, "/Volumes/music/虾米网易云/流行/03-01.人在旅途洒泪时-关正杰、雷安娜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/03-01.人在旅途洒泪时-关正杰、雷安娜.lrc"}, "/Volumes/music/虾米网易云/流行/03-02.上海滩-叶丽仪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/03-02.上海滩-叶丽仪.lrc"}, "/Volumes/music/虾米网易云/流行/03-06.京华春梦-汪明荃.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/03-06.京华春梦-汪明荃.lrc"}, "/Volumes/music/虾米网易云/流行/037.李克勤-月半小夜曲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/037.李克勤-月半小夜曲.lrc"}, "/Volumes/music/虾米网易云/流行/038.谭咏麟-讲不出再见.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/038.谭咏麟-讲不出再见.lrc"}, "/Volumes/music/虾米网易云/流行/04-07.旧梦不须记-雷安娜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/04-07.旧梦不须记-雷安娜.lrc"}, "/Volumes/music/虾米网易云/流行/041.何润东-没有我你怎么办.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/041.何润东-没有我你怎么办.lrc"}, "/Volumes/music/虾米网易云/流行/042.成龙_金喜善-美丽的神话.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/042.成龙_金喜善-美丽的神话.lrc"}, "/Volumes/music/虾米网易云/流行/050.关喆-想你的夜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/050.关喆-想你的夜.lrc"}, "/Volumes/music/虾米网易云/流行/054.黄龄-High歌.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/054.黄龄-High歌.lrc"}, "/Volumes/music/虾米网易云/流行/06-06.你的眼神-蔡琴.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/06-06.你的眼神-蔡琴.lrc"}, "/Volumes/music/虾米网易云/流行/06-07.偏偏喜欢你-陈百强.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/06-07.偏偏喜欢你-陈百强.lrc"}, "/Volumes/music/虾米网易云/流行/061.杨宗纬-洋葱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/061.杨宗纬-洋葱.lrc"}, "/Volumes/music/虾米网易云/流行/062.张雨生-大海.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/062.张雨生-大海.lrc"}, "/Volumes/music/虾米网易云/流行/065.永邦-你是我最深爱的人.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/065.永邦-你是我最深爱的人.lrc"}, "/Volumes/music/虾米网易云/流行/069.毛宁-涛声依旧.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/069.毛宁-涛声依旧.lrc"}, "/Volumes/music/虾米网易云/流行/070.庾澄庆-情非得已.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/070.庾澄庆-情非得已.lrc"}, "/Volumes/music/虾米网易云/流行/0710-难念的经-周华健.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/0710-难念的经-周华健.lrc"}, "/Volumes/music/虾米网易云/流行/073.张信哲-爱如潮水.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/073.张信哲-爱如潮水.lrc"}, "/Volumes/music/虾米网易云/流行/077.信乐团-死了都要爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/077.信乐团-死了都要爱.lrc"}, "/Volumes/music/虾米网易云/流行/08-01.雨夜的浪漫-谭咏麟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/08-01.雨夜的浪漫-谭咏麟.lrc"}, "/Volumes/music/虾米网易云/流行/08-06.蔓珠沙华-梅艳芳.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/08-06.蔓珠沙华-梅艳芳.lrc"}, "/Volumes/music/虾米网易云/流行/08-07.情已逝-张学友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/08-07.情已逝-张学友.lrc"}, "/Volumes/music/虾米网易云/流行/08-09.顺流·逆流-徐小凤.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/08-09.顺流·逆流-徐小凤.lrc"}, "/Volumes/music/虾米网易云/流行/08-10.爱情陷阱-谭咏麟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/08-10.爱情陷阱-谭咏麟.lrc"}, "/Volumes/music/虾米网易云/流行/080.无印良品-掌心.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/080.无印良品-掌心.lrc"}, "/Volumes/music/虾米网易云/流行/081.郑源-一万个理由.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/081.郑源-一万个理由.lrc"}, "/Volumes/music/虾米网易云/流行/084.周慧敏-最爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/084.周慧敏-最爱.lrc"}, "/Volumes/music/虾米网易云/流行/09-01.几许风雨-罗文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/09-01.几许风雨-罗文.lrc"}, "/Volumes/music/虾米网易云/流行/09-10.当年情-张国荣.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/09-10.当年情-张国荣.lrc"}, "/Volumes/music/虾米网易云/流行/0903-闪闪惹人爱-萧亚轩.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/0903-闪闪惹人爱-萧亚轩.lrc"}, "/Volumes/music/虾米网易云/流行/091.薛之谦-演员.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/091.薛之谦-演员.lrc"}, "/Volumes/music/虾米网易云/流行/092.张宇-月亮惹的祸.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/092.张宇-月亮惹的祸.lrc"}, "/Volumes/music/虾米网易云/流行/094.黄凯芹-雨中的恋人们.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/094.黄凯芹-雨中的恋人们.lrc"}, "/Volumes/music/虾米网易云/流行/097.刘嘉亮-你到底爱谁.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/097.刘嘉亮-你到底爱谁.lrc"}, "/Volumes/music/虾米网易云/流行/10-01.太阳星辰-张学友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/10-01.太阳星辰-张学友.lrc"}, "/Volumes/music/虾米网易云/流行/10-08.倾心-Raidas.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/10-08.倾心-Raidas.lrc"}, "/Volumes/music/虾米网易云/流行/100.孙露-鬼迷心窍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/100.孙露-鬼迷心窍.lrc"}, "/Volumes/music/虾米网易云/流行/1008-体面-于文文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1008-体面-于文文.lrc"}, "/Volumes/music/虾米网易云/流行/1011-天黑-阿杜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1011-天黑-阿杜.lrc"}, "/Volumes/music/虾米网易云/流行/1012-天黑黑.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1012-天黑黑.lrc"}, "/Volumes/music/虾米网易云/流行/1014-天后(Live)-薛之谦.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1014-天后(Live)-薛之谦.lrc"}, "/Volumes/music/虾米网易云/流行/1017-天亮了-韩红.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1017-天亮了-韩红.lrc"}, "/Volumes/music/虾米网易云/流行/11-04.真的汉子-林子祥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/11-04.真的汉子-林子祥.lrc"}, "/Volumes/music/虾米网易云/流行/1177-我只在乎你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1177-我只在乎你.lrc"}, "/Volumes/music/虾米网易云/流行/12-01.一生不变-李克勤.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/12-01.一生不变-李克勤.lrc"}, "/Volumes/music/虾米网易云/流行/12-02.谁明浪子心-王杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/12-02.谁明浪子心-王杰.lrc"}, "/Volumes/music/虾米网易云/流行/1207-喜欢你-G.E.M.邓紫棋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1207-喜欢你-G.E.M.邓紫棋.lrc"}, "/Volumes/music/虾米网易云/流行/13-04.你知道我在等你吗-张洪量.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/13-04.你知道我在等你吗-张洪量.lrc"}, "/Volumes/music/虾米网易云/流行/13-06.失恋-草蜢.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/13-06.失恋-草蜢.lrc"}, "/Volumes/music/虾米网易云/流行/14-01.每天爱你多一些-张学友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/14-01.每天爱你多一些-张学友.lrc"}, "/Volumes/music/虾米网易云/流行/14-03.一起走过的日子-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/14-03.一起走过的日子-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/1460-有多少爱可以重来-黄仲昆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1460-有多少爱可以重来-黄仲昆.lrc"}, "/Volumes/music/虾米网易云/流行/15-01.容易受伤的女人-王靖雯.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/15-01.容易受伤的女人-王靖雯.lrc"}, "/Volumes/music/虾米网易云/流行/15-06.真我的风采-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/15-06.真我的风采-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/1512-云烟成雨-徐薇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1512-云烟成雨-徐薇.lrc"}, "/Volumes/music/虾米网易云/流行/1518-再见只是陌生人(Live)-庄心妍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1518-再见只是陌生人(Live)-庄心妍.lrc"}, "/Volumes/music/虾米网易云/流行/1553-征服-那英.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1553-征服-那英.lrc"}, "/Volumes/music/虾米网易云/流行/1555-知否知否-胡夏&郁可唯.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1555-知否知否-胡夏&郁可唯.lrc"}, "/Volumes/music/虾米网易云/流行/1575-众里寻他-阿悠悠.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1575-众里寻他-阿悠悠.lrc"}, "/Volumes/music/虾米网易云/流行/1582-壮志在我胸-成龙.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1582-壮志在我胸-成龙.lrc"}, "/Volumes/music/虾米网易云/流行/1596-最爱 (Live)-周慧敏.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1596-最爱 (Live)-周慧敏.lrc"}, "/Volumes/music/虾米网易云/流行/16-02.夏日倾情-黎明.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/16-02.夏日倾情-黎明.lrc"}, "/Volumes/music/虾米网易云/流行/16-04.狂野之城-郭富城.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/16-04.狂野之城-郭富城.lrc"}, "/Volumes/music/虾米网易云/流行/16-05.谢谢你的爱-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/16-05.谢谢你的爱-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/1610-最近比较烦.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1610-最近比较烦.lrc"}, "/Volumes/music/虾米网易云/流行/1616-最远的你是我最近的爱-小曼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/1616-最远的你是我最近的爱-小曼.lrc"}, "/Volumes/music/虾米网易云/流行/17-01.忘情水-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/17-01.忘情水-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/17-03.心酸的情歌-巫启贤.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/17-03.心酸的情歌-巫启贤.lrc"}, "/Volumes/music/虾米网易云/流行/17-04.谁人知-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/17-04.谁人知-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/17-06.饿狼传说-张学友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/17-06.饿狼传说-张学友.lrc"}, "/Volumes/music/虾米网易云/流行/17岁 - 刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/17岁 - 刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/18-05.真永远-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/18-05.真永远-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/184沧海一声笑－许冠杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/184沧海一声笑－许冠杰.lrc"}, "/Volumes/music/虾米网易云/流行/19-01.情深说话未曾讲-黎明.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/19-01.情深说话未曾讲-黎明.lrc"}, "/Volumes/music/虾米网易云/流行/20-02.中国人-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/20-02.中国人-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/20-03.明知故犯-许美静.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/20-03.明知故犯-许美静.lrc"}, "/Volumes/music/虾米网易云/流行/20-04.爱的呼唤-郭富城.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/20-04.爱的呼唤-郭富城.lrc"}, "/Volumes/music/虾米网易云/流行/20-10.爱是永恒-张学友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/20-10.爱是永恒-张学友.lrc"}, "/Volumes/music/虾米网易云/流行/2018中国好声音 - 说散就散 (1).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/2018中国好声音 - 说散就散 (1).lrc"}, "/Volumes/music/虾米网易云/流行/205谁明浪子心 - 王杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/205谁明浪子心 - 王杰.lrc"}, "/Volumes/music/虾米网易云/流行/209忘了你忘了我－王杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/209忘了你忘了我－王杰.lrc"}, "/Volumes/music/虾米网易云/流行/21-06.越吻越伤心-苏永康.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/21-06.越吻越伤心-苏永康.lrc"}, "/Volumes/music/虾米网易云/流行/211笑看风云－郑少秋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/211笑看风云－郑少秋.lrc"}, "/Volumes/music/虾米网易云/流行/216雨中恋人们－黄凯芹.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/216雨中恋人们－黄凯芹.lrc"}, "/Volumes/music/虾米网易云/流行/22-04.木鱼与金鱼-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/22-04.木鱼与金鱼-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/22-06.非走不可-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/22-06.非走不可-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/22-09.插曲-郑秀文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/22-09.插曲-郑秀文.lrc"}, "/Volumes/music/虾米网易云/流行/22-11.眼睛想旅行-黎明.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/22-11.眼睛想旅行-黎明.lrc"}, "/Volumes/music/虾米网易云/流行/23-02.感情线上-郑秀文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/23-02.感情线上-郑秀文.lrc"}, "/Volumes/music/虾米网易云/流行/23-08.男人哭吧不是罪-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/23-08.男人哭吧不是罪-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/23-09.少女的祈祷-杨千嬅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/23-09.少女的祈祷-杨千嬅.lrc"}, "/Volumes/music/虾米网易云/流行/24-02.终身美丽-郑秀文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/24-02.终身美丽-郑秀文.lrc"}, "/Volumes/music/虾米网易云/流行/24-05.夏日Fiesta-刘德华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/24-05.夏日Fiesta-刘德华.lrc"}, "/Volumes/music/虾米网易云/流行/24-08.痛爱-容祖儿.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/24-08.痛爱-容祖儿.lrc"}, "/Volumes/music/虾米网易云/流行/25-04.好心分手-卢巧音.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/25-04.好心分手-卢巧音.lrc"}, "/Volumes/music/虾米网易云/流行/25-05.明年今日-陈奕迅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/25-05.明年今日-陈奕迅.lrc"}, "/Volumes/music/虾米网易云/流行/25-06.争气-容祖儿.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/25-06.争气-容祖儿.lrc"}, "/Volumes/music/虾米网易云/流行/26-02.三角志-卢巧音.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/26-02.三角志-卢巧音.lrc"}, "/Volumes/music/虾米网易云/流行/26-06.我的骄傲-容祖儿.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/26-06.我的骄傲-容祖儿.lrc"}, "/Volumes/music/虾米网易云/流行/26-08.好心好报-方力申.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/26-08.好心好报-方力申.lrc"}, "/Volumes/music/虾米网易云/流行/27-02.爱与诚-古巨基.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/27-02.爱与诚-古巨基.lrc"}, "/Volumes/music/虾米网易云/流行/27-03.饮歌-Twins.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/27-03.饮歌-Twins.lrc"}, "/Volumes/music/虾米网易云/流行/27-06.小城大事-杨千嬅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/27-06.小城大事-杨千嬅.lrc"}, "/Volumes/music/虾米网易云/流行/27-07.好好恋爱-方力申.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/27-07.好好恋爱-方力申.lrc"}, "/Volumes/music/虾米网易云/流行/28-03.无赖-郑中基.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/28-03.无赖-郑中基.lrc"}, "/Volumes/music/虾米网易云/流行/28-04.老鼠爱大米-王启文.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/28-04.老鼠爱大米-王启文.lrc"}, "/Volumes/music/虾米网易云/流行/281公里-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/281公里-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/29-04.情歌-侧田.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/29-04.情歌-侧田.lrc"}, "/Volumes/music/虾米网易云/流行/30-09.电灯胆-邓丽欣.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/30-09.电灯胆-邓丽欣.lrc"}, "/Volumes/music/虾米网易云/流行/31-02.喜贴街-谢安琪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/31-02.喜贴街-谢安琪.lrc"}, "/Volumes/music/虾米网易云/流行/31-03.一事无成-郑融、周柏豪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/31-03.一事无成-郑融、周柏豪.lrc"}, "/Volumes/music/虾米网易云/流行/31-08.爱不疚-林峰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/31-08.爱不疚-林峰.lrc"}, "/Volumes/music/虾米网易云/流行/32-03.你瞒我瞒-陈柏宇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/32-03.你瞒我瞒-陈柏宇.lrc"}, "/Volumes/music/虾米网易云/流行/33-05.天梯-CAllstar.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/33-05.天梯-CAllstar.lrc"}, "/Volumes/music/虾米网易云/流行/33-09.以身试爱-关心妍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/33-09.以身试爱-关心妍.lrc"}, "/Volumes/music/虾米网易云/流行/34-08.那些年-胡夏.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/34-08.那些年-胡夏.lrc"}, "/Volumes/music/虾米网易云/流行/34深夜港湾 - 海南小崇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/34深夜港湾 - 海南小崇.lrc"}, "/Volumes/music/虾米网易云/流行/Audrey Hepburn _ Misja Fitzgerald Michel - Moon River.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/<PERSON><PERSON> - Moon River.lrc"}, "/Volumes/music/虾米网易云/流行/BEYOND - 冷雨夜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/BEYOND - 冷雨夜.lrc"}, "/Volumes/music/虾米网易云/流行/BEYOND - 逝去日子.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/BEYOND - 逝去日子.lrc"}, "/Volumes/music/虾米网易云/流行/Babystop_山竹 - 盗将行.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Babystop_山竹 - 盗将行.lrc"}, "/Volumes/music/虾米网易云/流行/Mike 曾比特 - 初恋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Mike 曾比特 - 初恋.lrc"}, "/Volumes/music/虾米网易云/流行/M哥 - 七月上.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/M哥 - 七月上.lrc"}, "/Volumes/music/虾米网易云/流行/M哥 - 盗将行.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/M哥 - 盗将行.lrc"}, "/Volumes/music/虾米网易云/流行/M哥 - 陷阱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/M哥 - 陷阱.lrc"}, "/Volumes/music/虾米网易云/流行/M哥 - 雨蝶.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/M哥 - 雨蝶.lrc"}, "/Volumes/music/虾米网易云/流行/天后.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/天后.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我的根在草原-哈布尔.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我的根在草原-哈布尔.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Sara - 原来我爱你.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Sara - 原来我爱你.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/My will_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/My will_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/I Just Called To Say I Love Yo_Stevie Wonder.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/I Just Called To Say I Love Yo_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/宋冬野 - 安和桥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/宋冬野 - 安和桥.lrc"}, "/Volumes/music/虾米网易云/流行/Twins - 双失情人节.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Twins - 双失情人节.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/爱情在草原-夏兰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/爱情在草原-夏兰.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/naked mind_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/naked mind_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/何润东 - 没有我你怎么办.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/何润东 - 没有我你怎么办.lrc"}, "/Volumes/music/虾米网易云/流行/张镐哲-好男人.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张镐哲-好男人.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/火苗-格格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/火苗-格格.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/我可以抱你吗-张惠妹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/我可以抱你吗-张惠妹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - Get along.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - Get along.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/moumoon - Sunshine Girl.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/moumoon - Sunshine Girl.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/IN THIS ARM_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/IN THIS ARM_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/林晓培 - 那又如何.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/林晓培 - 那又如何.lrc"}, "/Volumes/music/虾米网易云/流行/赵传 - 爱要怎么说出口.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/赵传 - 爱要怎么说出口.lrc"}, "/Volumes/music/虾米网易云/流行/明年今日.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/明年今日.lrc"}, "/Volumes/music/虾米网易云/流行/Twins - 女校男生.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Twins - 女校男生.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/As Long as You Love Me_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/As Long as You Love Me_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/李健达 - 也许不易.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/李健达 - 也许不易.lrc"}, "/Volumes/music/虾米网易云/流行/无声仿有声.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/无声仿有声.lrc"}, "/Volumes/music/虾米网易云/流行/蔓珠沙华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/蔓珠沙华.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/If I were a Bird_中川幸太郎.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/If I were a <PERSON>_中川幸太郎.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我要去西藏-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我要去西藏-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/爱上一个不回家的人-林忆莲 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/爱上一个不回家的人-林忆莲 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/晴天.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/晴天.lrc"}, "/Volumes/music/虾米网易云/流行/难念的经.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/难念的经.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Marit Larsen - If A Song Could Get Me You.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON> - If A Song Could Get Me You.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/英雄-胡子 - 自从有了你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/英雄-胡子 - 自从有了你.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/火红的萨日朗-要不要买菜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/火红的萨日朗-要不要买菜.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/M2M - The Day You Went Away.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/M2M - The Day You Went Away.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/夜曲-周杰伦.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/夜曲-周杰伦.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/想你的时候-千百惠 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/想你的时候-千百惠 .lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/流浪的牧人-索朗扎西.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/流浪的牧人-索朗扎西.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Blue - All Rise.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Blue - All Rise.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/坐上火车去拉萨-徐千雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/坐上火车去拉萨-徐千雅.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Careless Whisper (Extended Mix_George Michael.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Careless Whisper (Extended Mix_George Michael.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Falling For You_原声带.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Falling For You_原声带.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/许志安 - 一步一生.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/许志安 - 一步一生.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/心花开在草原上-吉娜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/心花开在草原上-吉娜.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Beat It_Various Artists(1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Beat It_Various Artists(1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Big Big World  _Emilia.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Big Big World  _Emilia.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/王力宏 - 龙的传人.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/王力宏 - 龙的传人.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Mabical Vox - Power of ∞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Mabical Vox - Power of ∞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Believe_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Believe_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/又见高原红-容中尔甲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/又见高原红-容中尔甲.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Simon & Garfunkel - The Sound Of Silence.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> & Garfu<PERSON>el - The Sound Of Silence.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/永邦 - 每次都想呼喊你的名字.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/永邦 - 每次都想呼喊你的名字.lrc"}, "/Volumes/music/虾米网易云/动漫/林原惠美 - 《碧奇魂》尾曲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/林原惠美 - 《碧奇魂》尾曲.lrc"}, "/Volumes/music/虾米网易云/流行/味道-辛晓琪 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/味道-辛晓琪 .lrc"}, "/Volumes/music/虾米网易云/流行/朋友别哭.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/朋友别哭.lrc"}, "/Volumes/music/虾米网易云/流行/插曲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/插曲.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Sailing_Rod Stewart.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Sailing_Rod <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Peppino Gagliardi - Che Vuole Questa Musica Stasera [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON><PERSON><PERSON> - Che Vuole Questa Musica Stasera [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/庄心妍 - 魔鬼中的天使.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/庄心妍 - 魔鬼中的天使.lrc"}, "/Volumes/music/虾米网易云/流行/周杰伦、潘儿 - 夜的第七章.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰伦、潘儿 - 夜的第七章.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/月亮上的姑娘-三郎王青.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/月亮上的姑娘-三郎王青.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我的九寨-泽尔丹.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我的九寨-泽尔丹.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Spell_Marié Digby.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Spell_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Only You (And You Alone)_The Platters.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Only You (And You Alone)_The Platters.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Sometimes When We Touch _Olivia Ong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Sometimes When We Touch _Olivia Ong.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Music Is The Key (Video Versio_Sarah Connor.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Music Is The Key (Video Versio_Sarah <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/天上西藏-白玛多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/天上西藏-白玛多吉.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/fair - fair wind 秀逗魔导士.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/fair - fair wind 秀逗魔导士.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/海鸣威 - 我的回忆不是我的.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/海鸣威 - 我的回忆不是我的.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Adam Lambert - Mad World (American Idol Studio Version).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> (American Idol Studio Version).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/小石头和孩子们 _ 王一菲 _ 师葭希 - 盗将行.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/小石头和孩子们 _ 王一菲 _ 师葭希 - 盗将行.lrc"}, "/Volumes/music/虾米网易云/流行/周杰伦 - 夜曲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰伦 - 夜曲.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/遠い道の先で_武川アイ.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/遠い道の先で_武川アイ.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/海浪-黄品伟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/海浪-黄品伟.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Do As Infinity - 深い森.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Do As Infinity - 深い森.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Spice Girls - Viva Forever.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Spice Girls - Viva Forever.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/Pat The Cat _ Rachel Moreau - Hotel California.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/<PERSON> The Cat _ <PERSON> - Hotel California.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/想你的时候-千百惠 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/想你的时候-千百惠 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Can You Feel The Love Tonight _Elton John.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Can You Feel The Love Tonight _<PERSON> John.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Nothing's Gonna Change My Love_Westlife.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Nothing's Gonna Change My Love_Westlife.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Jessica Simpson - I Wanna Love You Forever.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - I Wanna Love You Forever.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/蔡国权 - 无心快语.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/蔡国权 - 无心快语.lrc"}, "/Volumes/music/虾米网易云/流行/陈粒 - 奇妙能力歌.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈粒 - 奇妙能力歌.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我从草原来-萨顶顶.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我从草原来-萨顶顶.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Valentine_Jim Brickman.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/慕情_和田薫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/慕情_和田薫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/郭顶-我们俩.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/郭顶-我们俩.lrc"}, "/Volumes/music/虾米网易云/流行/争气.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/争气.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Dangerous_Michael Jackson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Dangerous_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/S.H.E - 恋人未满.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/S.H.E - 恋人未满.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/α·Pav - 桜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/α·Pav - 桜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/雷诺儿 - 别在我离开之前离开.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/雷诺儿 - 别在我离开之前离开.lrc"}, "/Volumes/music/虾米网易云/流行/你知道我在等你吗.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/你知道我在等你吗.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/彩云之南-徐千雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/彩云之南-徐千雅.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Moon River_Henry Mancini.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Moon River_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/草蜢 - 情歌 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/草蜢 - 情歌 (Live).lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Fly Me To The Moon_Olivia Ong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Fly Me To The Moon_Olivia Ong.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Tamas Wells - Valder Fields.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Tamas Wells - Valder Fields.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/brave heart_宮崎歩.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/brave heart_宮崎歩.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/刘若英 - 后来.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘若英 - 后来.lrc"}, "/Volumes/music/虾米网易云/流行/安又琪 - 有你陪着我.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/安又琪 - 有你陪着我.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Moon River_Audrey Hepburn.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON>_Audrey <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/星语心愿-张柏芝 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/星语心愿-张柏芝 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/火辣辣的情歌-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/火辣辣的情歌-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/流行/不装饰你的梦-蔡国权 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/不装饰你的梦-蔡国权 .lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Say you say me _Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Say you say me _Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/无情的情书-动力火车 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/无情的情书-动力火车 .lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Pretty Boy_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Pretty Boy_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/永邦 - 威尼斯的泪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/永邦 - 威尼斯的泪.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/More Than I Can Say_原声带.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/More Than I Can Say_原声带.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/陶喆 - 爱，很简单.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陶喆 - 爱，很简单.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Shape Of My Heart_Sting.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Shape Of My Heart_Sting.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/梦醒时分-陈淑华 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/梦醒时分-陈淑华 .lrc"}, "/Volumes/music/虾米网易云/流行/夏日倾情.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/夏日倾情.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - KUJIKENAIKARA!.ogg/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - KUJIKENAIKARA!.ogg/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Secret_岡崎律子.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Secret_岡崎律子.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/手捧奶酒敬亲人-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/手捧奶酒敬亲人-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/流行/用心良苦-张宇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/用心良苦-张宇.lrc"}, "/Volumes/music/虾米网易云/流行/老人与海 - 海鸣威.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/老人与海 - 海鸣威.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/One Match_Sarah Harmer.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/One Match_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Babyface - Drama, Love & 'Lationships [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Babyface - Drama, Love & 'Lationships [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/边走边爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/边走边爱.lrc"}, "/Volumes/music/虾米网易云/流行/感情线上.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/感情线上.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/時代を越える想い~Piano Concerto~(Mosco_和田薫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/時代を越える想い~Piano Concerto~(<PERSON><PERSON>_和田薫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Maria Arredondo - Burning.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Maria Arredondo - Burning.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Mariangela - Ninna Nanna.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON><PERSON> - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/海浪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/海浪.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/All Rise_Blue.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/All Rise_Blue.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/檄!帝国華撃団_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/檄!帝国華撃団_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/周传雄-黄昏.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周传雄-黄昏.lrc"}, "/Volumes/music/虾米网易云/流行/李晓杰 - 好姑娘.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/李晓杰 - 好姑娘.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Hear Me Cry _原声带.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Hear Me Cry _原声带.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/陈雪凝 - 你的酒馆对我打了烊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈雪凝 - 你的酒馆对我打了烊.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Mad World_American Idol Contestant Performances.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Mad World_American Idol Contestant Performances.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/N・O・V・A - FAIR WIND.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/N・O・V・A - FAIR WIND.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/许嵩 - 有何不可.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/许嵩 - 有何不可.lrc"}, "/Volumes/music/虾米网易云/流行/陈诚 - 胆小鬼 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈诚 - 胆小鬼 (Live).lrc"}, "/Volumes/music/虾米网易云/流行/现代爱情故事-张智霖.许秋怡 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/现代爱情故事-张智霖.许秋怡 .lrc"}, "/Volumes/music/虾米网易云/流行/你的眼睛背叛你的心-郑中基 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/你的眼睛背叛你的心-郑中基 .lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/格桑拉-白玛多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/格桑拉-白玛多吉.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/For Fruits Basket -on Air Vers_岡崎律子.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/For Fruits Basket -on Air Vers_岡崎律子.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Because I Love You_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Because I Love You_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/我这个你不爱的人-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/我这个你不爱的人-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/边走边爱-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/边走边爱-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/冷酷到底-羽泉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/冷酷到底-羽泉.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Dynamic Black - Yesterday.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Dynamic Black - Yesterday.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/谁人知.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/谁人知.lrc"}, "/Volumes/music/虾米网易云/流行/雷军 - Are You OK.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/雷军 - Are You OK.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Mysterious Lands_Christopher Caouette.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Mysterious Lands_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/陈奕迅 - 裙下之臣.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈奕迅 - 裙下之臣.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Simon & Garfunkel - Scarborough Fair Canticle.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Simon & Garfunkel - Scarborough Fair Canticle.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Christina Aguilera - Save Me From Myself.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Save Me From Myself.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/张远喆 - 不配做你男朋友.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张远喆 - 不配做你男朋友.lrc"}, "/Volumes/music/虾米网易云/流行/冷酷到底-羽泉 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/冷酷到底-羽泉 .lrc"}, "/Volumes/music/虾米网易云/流行/海浪-黄品伟 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/海浪-黄品伟 .lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lune_Riccardo Cocciante.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON>_<PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/月光洒在草原上-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/月光洒在草原上-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Richard Marx - Right Here Waiting.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Right Here Waiting.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/我不想说.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/我不想说.lrc"}, "/Volumes/music/虾米网易云/流行/草蜢 - 不走回头路.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/草蜢 - 不走回头路.lrc"}, "/Volumes/music/虾米网易云/流行/马頔 - 南山南.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/马頔 - 南山南.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/天下最美的草原-呼斯楞.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/天下最美的草原-呼斯楞.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Britney Spears - Baby One More Time.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON><PERSON> Spears - Baby One More Time.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/蒙娜丽莎的眼泪-林志炫 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/蒙娜丽莎的眼泪-林志炫 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/游牧情歌 (DJ版)-何鹏&格格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/游牧情歌 (DJ版)-何鹏&格格.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Better Man_Robbie Williams.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Better Man_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/笑看风云.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/笑看风云.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Dilemma_Nelly.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Dilemma_Nelly.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/林原惠美 - 《碧奇魂》尾曲.ogg/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/林原惠美 - 《碧奇魂》尾曲.ogg/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/五星戦隊ダイレンジャー_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/五星戦隊ダイレンジャー_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/张根硕 - Fly Me To The Moon.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/张根硕 - Fly Me To The Moon.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/TVB迪士尼奇幻世界-阿拉丁主 粵語 片頭曲（非完整版）阿拉丁《 心裡願望》 主唱：盧維昌.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/TVB迪士尼奇幻世界-阿拉丁主 粵語 片頭曲（非完整版）阿拉丁《 心裡願望》 主唱：盧維昌.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/吉祥赞-郭彦华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/吉祥赞-郭彦华.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/情非得已-庚澄庆 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/情非得已-庚澄庆 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/卓玛-亚东.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/卓玛-亚东.lrc"}, "/Volumes/music/虾米网易云/流行/伤痕-林忆莲 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/伤痕-林忆莲 .lrc"}, "/Volumes/music/虾米网易云/流行/伍咏薇 - 再生天地.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/伍咏薇 - 再生天地.lrc"}, "/Volumes/music/虾米网易云/流行/罗嘉良 - 差一刹的地老天荒.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/罗嘉良 - 差一刹的地老天荒.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/姑娘我爱你-白玛多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/姑娘我爱你-白玛多吉.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/让我一次爱个够-庾澄庆 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/让我一次爱个够-庾澄庆 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Best of Me_Daniel Powter.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Best of Me_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/爱从草原来-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/爱从草原来-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/家乡的牧场-蓝琪儿.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/家乡的牧场-蓝琪儿.lrc"}, "/Volumes/music/虾米网易云/流行/因为爱所以爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/因为爱所以爱.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/KEEP ON DREAMING_日本群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/KEEP ON DREAMING_日本群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/金志文 _ 沈凌 _ 千斤组合 - 当 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/金志文 _ 沈凌 _ 千斤组合 - 当 (Live).lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Feeling Good (American Idol St_Adam Lambert.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Feeling Good (American Idol <PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我和草原有个约定-凤凰传奇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我和草原有个约定-凤凰传奇.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Declan Galbraith - Tell Me Why.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Tell Me Why.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/草蜢 - 嗲噢 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/草蜢 - 嗲噢 (Live).lrc"}, "/Volumes/music/虾米网易云/流行/杨臣刚 - 老鼠爱大米.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/杨臣刚 - 老鼠爱大米.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Take Me To Your Heart_Michael Learns To Rock.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Take Me To Your Heart_Michael Learns To Rock.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/余文乐 - 司机.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/余文乐 - 司机.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/C'est L'amour_Rosi Golan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/C'est L'amour_<PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/无声仿有声-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/无声仿有声-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Westlife - You Raise Me Up.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Westlife - You Raise Me Up.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/味道-辛晓琪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/味道-辛晓琪.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Power Play(TVサイズ)_堀井勝美.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Power Play(TVサイズ)_堀井勝美.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/陈奕迅 - 孤勇者.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈奕迅 - 孤勇者.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/群星 - Lemon Tree [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/群星 - Lemon Tree [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Buttons_The Pussycat Dolls.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Buttons_The Pussycat Dolls.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/满文军-懂你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/满文军-懂你.lrc"}, "/Volumes/music/虾米网易云/流行/潜龙勿用-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/潜龙勿用-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/倾心.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/倾心.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Darren Hayes - Creepin' Up On You.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - <PERSON>pin' Up On You.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/IN THIS ARM_奥井雅美.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/IN THIS ARM_奥井雅美.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/失恋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/失恋.lrc"}, "/Volumes/music/虾米网易云/流行/卓依婷 - 明天会更好.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/卓依婷 - 明天会更好.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我的新娘在草原-荣联合.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我的新娘在草原-荣联合.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/宇宙刑警 Op - Infinity.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/宇宙刑警 Op - Infinity.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/非走不可-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/非走不可-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/王杰 - 人在风雨中 (2001香港WANG'S 演唱会).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/王杰 - 人在风雨中 (2001香港WANG'S 演唱会).lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/天下最美-格格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/天下最美-格格.lrc"}, "/Volumes/music/虾米网易云/流行/人在旅途洒泪时.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/人在旅途洒泪时.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/all over love_LOVE PSYCHEDELICO.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/all over love_LOVE PSYCHEDELICO.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/灼熱の恋_林原めぐみ.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/灼熱の恋_林原めぐみ.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - Get along.ogg/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - Get along.ogg/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/周深 - 化身孤岛的鲸.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周深 - 化身孤岛的鲸.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Take My Breath Away_Berlin.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Take My Breath Away_Berlin.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/op 忍者战队 主题曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/op 忍者战队 主题曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/半阳 - 流浪.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/半阳 - 流浪.lrc"}, "/Volumes/music/虾米网易云/流行/现代爱情故事-张智霖.许秋怡.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/现代爱情故事-张智霖.许秋怡.lrc"}, "/Volumes/music/虾米网易云/流行/情已逝.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/情已逝.lrc"}, "/Volumes/music/虾米网易云/流行/情歌情哥 - 两只蝴蝶.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/情歌情哥 - 两只蝴蝶.lrc"}, "/Volumes/music/虾米网易云/流行/几许风雨.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/几许风雨.lrc"}, "/Volumes/music/虾米网易云/流行/冰冷长街-王杰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/冰冷长街-王杰.lrc"}, "/Volumes/music/虾米网易云/流行/陶喆 - 就是爱你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陶喆 - 就是爱你.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/大高原-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/大高原-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/心中的高原-降央卓玛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/心中的高原-降央卓玛.lrc"}, "/Volumes/music/虾米网易云/流行/冰冷长街.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/冰冷长街.lrc"}, "/Volumes/music/虾米网易云/流行/周杰伦 - 七里香.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰伦 - 七里香.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/为你等待 (藏语版)-次仁央宗.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/为你等待 (藏语版)-次仁央宗.lrc"}, "/Volumes/music/虾米网易云/流行/Twins - 恋爱大过天.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Twins - 恋爱大过天.lrc"}, "/Volumes/music/虾米网易云/流行/多亮 - 小情歌.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/多亮 - 小情歌.lrc"}, "/Volumes/music/虾米网易云/流行/雷军 - 曾经的你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/雷军 - 曾经的你.lrc"}, "/Volumes/music/虾米网易云/流行/薛之谦-演员.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/薛之谦-演员.lrc"}, "/Volumes/music/虾米网易云/流行/周深-大鱼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周深-大鱼.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/敖包相会-降央卓玛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/敖包相会-降央卓玛.lrc"}, "/Volumes/music/虾米网易云/流行/光良 - 童话.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/光良 - 童话.lrc"}, "/Volumes/music/虾米网易云/动漫/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - KUJIKENAIKARA!.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/奥井雅美 (おくい まさみ) _ 林原惠美 (林原めぐみ) - KUJIKENAIKARA!.lrc"}, "/Volumes/music/虾米网易云/流行/头发乱了.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/头发乱了.lrc"}, "/Volumes/music/虾米网易云/流行/谢谢你的爱1999.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/谢谢你的爱1999.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/運命と恋心_和田薫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/運命と恋心_和田薫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/伍珂玥 - 蔓珠莎华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/伍珂玥 - 蔓珠莎华.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Jessica Simpson - When You Told Me You Loved Me.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - When You Told Me You Loved Me.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Gringoire (Bruno Pelletier) - Le Temps Des Cathedrales.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> (<PERSON>) - Le Temps Des Cathedrales.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/基因改造·阿门-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/基因改造·阿门-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/杨千嬅 - 可惜我是水瓶座.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/杨千嬅 - 可惜我是水瓶座.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/V6 - CHANGE THE WORLD.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/V6 - CHANGE THE WORLD.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/你的样子.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/你的样子.lrc"}, "/Volumes/music/虾米网易云/流行/凤凰传奇 - 月亮之上.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/凤凰传奇 - 月亮之上.lrc"}, "/Volumes/music/虾米网易云/流行/玉蝴蝶-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/玉蝴蝶-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Butter-fly_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Butter-fly_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/李瑨瑶 - 易燃易爆炸.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/李瑨瑶 - 易燃易爆炸.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/想你想到草绿了-小颖.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/想你想到草绿了-小颖.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Karma_阿保剛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Karma_阿保剛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/周慧敏-最爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周慧敏-最爱.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Billy Gilman - Everything and More.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Everything and More.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/活着VIVA-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/活着VIVA-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/陈粒 - 走马.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈粒 - 走马.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我的根在草原-德德玛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我的根在草原-德德玛.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Love to Be Loved by You (The W_Marc Terenzi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Love to Be Loved by You (The W_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/一千零一个愿望-4inlove .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/一千零一个愿望-4inlove .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Fever_Don Williams.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Fever_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Laura Pausini - It's Not Good-Bye.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - It's Not Good-Bye.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/伤痕-林忆莲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/伤痕-林忆莲.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Craig David - You Don`t Miss Your Water.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - You Don`t Miss Your Water.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/刘德华 - 恭喜发财.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘德华 - 恭喜发财.lrc"}, "/Volumes/music/虾米网易云/流行/无情的情书-动力火车.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/无情的情书-动力火车.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Jewel - Stand.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Jewel - Stand.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/爱的呼唤.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/爱的呼唤.lrc"}, "/Volumes/music/虾米网易云/流行/平安 - 洋葱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/平安 - 洋葱.lrc"}, "/Volumes/music/虾米网易云/流行/丁香花.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/丁香花.lrc"}, "/Volumes/music/虾米网易云/流行/大海.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/大海.lrc"}, "/Volumes/music/虾米网易云/流行/顺流·逆流.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/顺流·逆流.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Departure(Piano Solo)_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Departure(Piano Solo)_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/摩訶不思議アドベンチャー! _日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/摩訶不思議アドベンチャー! _日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/火红的萨日朗-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/火红的萨日朗-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/FANTASY_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/FANTASY_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/用心良苦-张宇 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/用心良苦-张宇 .lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Casablanca_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Casablanca_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/姑娘我爱你-索朗扎西.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/姑娘我爱你-索朗扎西.lrc"}, "/Volumes/music/虾米网易云/流行/天梯.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/天梯.lrc"}, "/Volumes/music/虾米网易云/流行/李泽垚 - 带你去旅行 (偷耳机版).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/李泽垚 - 带你去旅行 (偷耳机版).lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Play - Power - 爆裂战士至篮保.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Play - Power - 爆裂战士至篮保.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Dilemma Featuring Kelly Rowlan_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Dilemma Featuring <PERSON>_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/古天乐 - 吻得到，爱不到.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/古天乐 - 吻得到，爱不到.lrc"}, "/Volumes/music/虾米网易云/流行/刘若英 - 很爱很爱你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘若英 - 很爱很爱你.lrc"}, "/Volumes/music/虾米网易云/流行/一生何求.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/一生何求.lrc"}, "/Volumes/music/虾米网易云/流行/夏日Fiesta.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/夏日Fiesta.lrc"}, "/Volumes/music/虾米网易云/流行/陈小春 - 独家记忆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈小春 - 独家记忆.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Black Black Heart_David Usher.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Black Black Heart_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/我们这里还有鱼-谢霆锋&黄大炜&游鸿明&黄中原.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/我们这里还有鱼-谢霆锋&黄大炜&游鸿明&黄中原.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Boston_Augustana.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Boston_Augustana.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Jason MrazColbie Caillat - Lucky [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Lucky [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/山下智久 - colorful - 龙樱插曲.mp3/SYNOAUDIO_SONG_RATING": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/山下智久 - colorful - 龙樱插曲.mp3/SYNOAUDIO_SONG_RATING"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lucky Star_Joana Zimmer.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Lucky Star_<PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/RIKKI - 素敌だね.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/RIKKI - 素敌だね.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/枕着你的名字入睡.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/枕着你的名字入睡.lrc"}, "/Volumes/music/虾米网易云/流行/鍠滄浣?.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/鍠滄浣?.lrc"}, "/Volumes/music/虾米网易云/流行/张学友 - 夕阳醉了.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张学友 - 夕阳醉了.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Concerto pour deux voix_Saint-Preux.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Concerto pour deux voix_Saint-Preux.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/That's Why (You Go Away)_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/That's Why (You Go Away)_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/唱首情歌给草原 (环绕声效)-罗海英.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/唱首情歌给草原 (环绕声效)-罗海英.lrc"}, "/Volumes/music/虾米网易云/流行/周杰伦 - 稻香.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰伦 - 稻香.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/梦回云南 (DJ小鱼儿版)-白玛多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/梦回云南 (DJ小鱼儿版)-白玛多吉.lrc"}, "/Volumes/music/虾米网易云/流行/甄子丹 - 理想.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/甄子丹 - 理想.lrc"}, "/Volumes/music/虾米网易云/流行/陈粒 - 易燃易爆炸.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈粒 - 易燃易爆炸.lrc"}, "/Volumes/music/虾米网易云/流行/雨夜的浪漫.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/雨夜的浪漫.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lemon Tree_박혜경.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Lemon Tree_박혜경.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/张玮 - High歌.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张玮 - High歌.lrc"}, "/Volumes/music/虾米网易云/流行/蔡枫华 - 绝对空虚.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/蔡枫华 - 绝对空虚.lrc"}, "/Volumes/music/虾米网易云/流行/当年情.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/当年情.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Better Off_Lindsey Ray.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Better Off_Lindsey Ray.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/活着VIVA.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/活着VIVA.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/All Out of Love_Air Supply.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/All Out of Love_Air Supply.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/每次都想呼喊你的名字.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/每次都想呼喊你的名字.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/I’m Yours_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/I’m Yours_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/口是心非.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/口是心非.lrc"}, "/Volumes/music/虾米网易云/流行/最近比较烦.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/最近比较烦.lrc"}, "/Volumes/music/虾米网易云/流行/杨宗纬-洋葱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/杨宗纬-洋葱.lrc"}, "/Volumes/music/虾米网易云/流行/你的眼神.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/你的眼神.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/奥井雅美 (おくい まさみ) - 邪魔はさせない (请勿打扰).flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/奥井雅美 (おくい まさみ) - 邪魔はさせない (请勿打扰).flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/风墨Lion - 夜曲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/风墨Lion - 夜曲.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Brand-New World_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Brand-New World_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/melody. - Realize.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/melody. - Realize.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Bitter Heart_Zee Avi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Bitter Heart_Zee Avi.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/庞 龙 ⧸ 两 只 蝴 蝶.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/庞 龙 ⧸ 两 只 蝴 蝶.lrc"}, "/Volumes/music/虾米网易云/流行/张卫健 - 虚虚实实.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张卫健 - 虚虚实实.lrc"}, "/Volumes/music/虾米网易云/流行/蕾蕾的小麦霸们 - 赢在江湖.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/蕾蕾的小麦霸们 - 赢在江湖.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/天籁之爱-容中尔甲&旺姆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/天籁之爱-容中尔甲&旺姆.lrc"}, "/Volumes/music/虾米网易云/流行/Twins - 朋友仔.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Twins - 朋友仔.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/以来你什么都不要-张惠妹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/以来你什么都不要-张惠妹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/林俊杰 - 江南.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/林俊杰 - 江南.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我的草原我的缘-司徒兰芳.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我的草原我的缘-司徒兰芳.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/I am_和田薫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/I am_和田薫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/周杰 _ 林心如 - 你是风儿我是沙.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰 _ 林心如 - 你是风儿我是沙.lrc"}, "/Volumes/music/虾米网易云/流行/好心分手.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/好心分手.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Apologize_Timbaland.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Apologize_Timbaland.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lucky _Colbie Caillat.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> _<PERSON>bie Caillat.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/She's Gone_Steelheart.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/She's Gone_Steelheart.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/溜溜的姑娘像朵花-龚玥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/溜溜的姑娘像朵花-龚玥.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/爱我的人和我爱的人-游鸿明 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/爱我的人和我爱的人-游鸿明 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/张杰 - 逆战.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张杰 - 逆战.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/潜龙勿用.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/潜龙勿用.lrc"}, "/Volumes/music/虾米网易云/流行/Twins - 多谢失恋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Twins - 多谢失恋.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/勇気100％　（アニメ「忍たま乱太郎」主題歌）_日本群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/勇気100％　（アニメ「忍たま乱太郎」主題歌）_日本群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Free Loop_Daniel Powter.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Free Loop_Daniel <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/梁咏琪 - 两个人的幸运.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/梁咏琪 - 两个人的幸运.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/天涯情歌-夏兰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/天涯情歌-夏兰.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/A Better Day_JTL.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/A Better Day_JTL.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/刘明湘 - 漂洋过海来看你 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘明湘 - 漂洋过海来看你 (Live).lrc"}, "/Volumes/music/虾米网易云/流行/三角志.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/三角志.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/套马杆-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/套马杆-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/再见也是朋友-何婉盈.曾航生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/再见也是朋友-何婉盈.曾航生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Better Together_Jack Johnson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Better Together_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/Sweety - 勇敢的幸福.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Sweety - 勇敢的幸福.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lenka - Trouble Is A Friend.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Lenka - Trouble Is A Friend.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Toxic_CALLmeKAT.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Toxic_CALLmeKAT.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/周杰伦 - Mojito.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/周杰伦 - Mojito.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/山下智久 - colorful - 龙樱插曲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/山下智久 - colorful - 龙樱插曲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Grip!_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Grip!_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/桑岛法子 - Somewhere - 魔剑美神 Try 最终回 Ed.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/桑岛法子 - Somewhere - 魔剑美神 Try 最终回 Ed.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/罗嘉良 _ 张可颐 - 奇哥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/罗嘉良 _ 张可颐 - 奇哥.lrc"}, "/Volumes/music/虾米网易云/流行/草蜢 - 习惯失恋 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/草蜢 - 习惯失恋 (Live).lrc"}, "/Volumes/music/虾米网易云/流行/Ȫ-.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/Ȫ-.lrc"}, "/Volumes/music/虾米网易云/流行/金佩珊 - 保镖.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/金佩珊 - 保镖.lrc"}, "/Volumes/music/虾米网易云/流行/吴奇隆 _ 苏有朋 - 祝你一路顺风 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/吴奇隆 _ 苏有朋 - 祝你一路顺风 (Live).lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Let Me Go_MOCCA.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Let Me Go_MOCCA.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/张敬轩 - 我的宝贝 (粤语).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张敬轩 - 我的宝贝 (粤语).lrc"}, "/Volumes/music/虾米网易云/流行/越吻越伤心.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/越吻越伤心.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Hotel California (Live)_Eagles.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Hotel California (Live)_Eagles.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/杰洛士D - 【秀逗魔导士】Breeze.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/杰洛士D - 【秀逗魔导士】Breeze.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Sarah Brightman - Scarborough Fair.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Scarborough Fair.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/我们这里还有鱼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/我们这里还有鱼.lrc"}, "/Volumes/music/虾米网易云/流行/太阳星辰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/太阳星辰.lrc"}, "/Volumes/music/虾米网易云/流行/一笑而过-那英 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/一笑而过-那英 .lrc"}, "/Volumes/music/虾米网易云/流行/陈慧娴 - 明日有明天.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈慧娴 - 明日有明天.lrc"}, "/Volumes/music/虾米网易云/流行/眼睛想旅行.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/眼睛想旅行.lrc"}, "/Volumes/music/虾米网易云/流行/非走不可.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/非走不可.lrc"}, "/Volumes/music/虾米网易云/流行/偏偏喜欢你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/偏偏喜欢你.lrc"}, "/Volumes/music/虾米网易云/流行/光良 - 第一次.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/光良 - 第一次.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Alan Jackson - Remember When.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> - Remember When.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/难得有情人-关淑怡 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/难得有情人-关淑怡 .lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Celine Dion - My Heart Will Go On.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON> - My Heart Will Go On.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/歌在飞-苏勒亚其其格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/歌在飞-苏勒亚其其格.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/梦中的雪莲花-巴桑拉姆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/梦中的雪莲花-巴桑拉姆.lrc"}, "/Volumes/music/虾米网易云/流行/爱后余生-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/爱后余生-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/郑伊健、陈小春 - 热血燃烧.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/郑伊健、陈小春 - 热血燃烧.lrc"}, "/Volumes/music/虾米网易云/流行/熊宝贝乐团 - 环岛旅行.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/熊宝贝乐团 - 环岛旅行.lrc"}, "/Volumes/music/虾米网易云/流行/谢谢你的爱1999-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/谢谢你的爱1999-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Build You Up_Kim Taylor.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Build You Up_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/好男人.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/好男人.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Every Heart_BoA.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Every Heart_BoA.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/每天爱你多一些.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/每天爱你多一些.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Marit Larsen - If A Song Could Get Me You [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON> - If A Song Could Get Me You [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/雨中恋人们.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/雨中恋人们.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Rhythm of the Rain_Jason Donovan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Rhythm of the Rain_Jason Donovan.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/刘嘉亮-你到底爱谁.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘嘉亮-你到底爱谁.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Father and Son_Cat Stevens.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Father and Son_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/最炫民族风-凤凰传奇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/最炫民族风-凤凰传奇.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/I&Myself_林原めぐみ.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/I&Myself_林原めぐみ.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/摩登兄弟 - 我的秘密.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/摩登兄弟 - 我的秘密.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/唐古拉-王麟&完玛三智.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/唐古拉-王麟&完玛三智.lrc"}, "/Volumes/music/虾米网易云/流行/一生不变.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/一生不变.lrc"}, "/Volumes/music/虾米网易云/流行/你的柔情我永远不懂.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/你的柔情我永远不懂.lrc"}, "/Volumes/music/虾米网易云/流行/游乐场-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/游乐场-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/古巨基 - 烟雨濛濛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/古巨基 - 烟雨濛濛.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Lullaby_David Roth.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON><PERSON><PERSON>_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/眼泪为你流.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/眼泪为你流.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/最浪漫的事-赵咏华 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/最浪漫的事-赵咏华 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/我从草原来-凤凰传奇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/我从草原来-凤凰传奇.lrc"}, "/Volumes/music/虾米网易云/流行/费翔 - 冬天里的一把火.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/费翔 - 冬天里的一把火.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/For フルーツバスケット  _岡崎律子.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/For フルーツバスケット  _岡崎律子.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/因为爱所以爱-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/因为爱所以爱-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/杰洛士D - 【秀逗魔导士】Breeze.ogg/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/杰洛士D - 【秀逗魔导士】Breeze.ogg/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Nothing's Gonna Change My Love_George Benson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Nothing's Gonna Change My Love_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Moon River_David Davidson.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Moon River_<PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/囚鸟-彭羚 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/囚鸟-彭羚 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/DAN DAN 心魅かれてく_FIELD OF VIEW.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/DAN DAN 心魅かれてく_FIELD OF VIEW.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Michael Jackson - Dangerous [mqms2].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/<PERSON> [mqms2].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/一笑而过-那英 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/一笑而过-那英 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/痛你的责任-品冠 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/痛你的责任-品冠 .lrc"}, "/Volumes/music/虾米网易云/流行/是嗡嗡呀 - 《两只蝴蝶》—庞龙.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/是嗡嗡呀 - 《两只蝴蝶》—庞龙.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/大草原-四海吉声文化.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/大草原-四海吉声文化.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Dearest (ストリングスVer．)_和田薫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Dearest (ストリングスVer．)_和田薫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Beat It_Various Artists.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Beat It_Various Artists.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/爱如潮水-张信哲 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/爱如潮水-张信哲 .lrc"}, "/Volumes/music/虾米网易云/流行/摩登兄弟刘宇宁 - 讲真的.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/摩登兄弟刘宇宁 - 讲真的.lrc"}, "/Volumes/music/虾米网易云/流行/黄种人-谢霆锋.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/黄种人-谢霆锋.lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/在我生命里-罗文 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/在我生命里-罗文 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Bizarre Love Triangle_Frente!.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Bizarre Love Triangle_Frente!.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/京华春梦.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/京华春梦.lrc"}, "/Volumes/music/虾米网易云/流行/刘刚 - 怀念青春.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/刘刚 - 怀念青春.lrc"}, "/Volumes/music/虾米网易云/流行/晴天-周杰伦.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/晴天-周杰伦.lrc"}, "/Volumes/music/虾米网易云/流行/张学友 - 我应该.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张学友 - 我应该.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/爱情海-央金兰泽.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/爱情海-央金兰泽.lrc"}, "/Volumes/music/虾米网易云/流行/心里愿望-未知歌手[aq].lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/心里愿望-未知歌手[aq].lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Dear friends_日本ACG.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Dear friends_日本ACG.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/约定-周惠.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/约定-周惠.lrc"}, "/Volumes/music/虾米网易云/流行/爱情陷阱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/爱情陷阱.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/L・O・N - BRAVE SOULS~give a reason.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/L・O・N - BRAVE SOULS~give a reason.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/动漫/@eaDir/IQ博士_群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/IQ博士_群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/接新娘-皆大欢喜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/接新娘-皆大欢喜.lrc"}, "/Volumes/music/虾米网易云/流行/安以轩 - 如果那天没有遇见你.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/安以轩 - 如果那天没有遇见你.lrc"}, "/Volumes/music/虾米网易云/流行/方琼 - 山水迢迢.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/方琼 - 山水迢迢.lrc"}, "/Volumes/music/虾米网易云/流行/关诗敏 - 恋人未满.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/关诗敏 - 恋人未满.lrc"}, "/Volumes/music/虾米网易云/流行/张芸京 - 偏爱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张芸京 - 偏爱.lrc"}, "/Volumes/music/虾米网易云/流行/罗嘉良 _ 张可颐 _ 宣萱 _ 吴镇宇 - 难兄难弟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/罗嘉良 _ 张可颐 _ 宣萱 _ 吴镇宇 - 难兄难弟.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/Sweet Dream_장나라.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Sweet Dream_장나라.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/圣洁的西藏-巴桑拉姆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/圣洁的西藏-巴桑拉姆.lrc"}, "/Volumes/music/虾米网易云/流行/melody_ (メロディー) - realize.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/melody_ (メロディー) - realize.lrc"}, "/Volumes/music/虾米网易云/流行/方琼 - 今日天气好晴朗.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/方琼 - 今日天气好晴朗.lrc"}, "/Volumes/music/虾米网易云/流行/情深说话未曾讲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/情深说话未曾讲.lrc"}, "/Volumes/music/虾米网易云/动漫/@eaDir/Fly Me to the Moon_Joshua Radin.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "动漫/@eaDir/Fly Me to the Moon_<PERSON> Radin.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/陈慧琳 - 她比我丑.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/陈慧琳 - 她比我丑.lrc"}, "/Volumes/music/虾米网易云/流行/何耀珊 - 你的肩膀.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/何耀珊 - 你的肩膀.lrc"}, "/Volumes/music/虾米网易云/外文/@eaDir/If You Come to Me_Atomic Kitten(1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/If You Come to Me_Atomic Kitten(1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/外文/@eaDir/Bye Bye Bye_Lovestoned.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "外文/@eaDir/Bye Bye Bye_Lovestoned.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/牧人-追风骏马组合.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/牧人-追风骏马组合.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/牧羊姑娘 (平四版)-华语群星.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/牧羊姑娘 (平四版)-华语群星.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/神奇的九寨-容中尔甲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/神奇的九寨-容中尔甲.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/离别草原-云飞.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/离别草原-云飞.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/等你在草原-阿桑·格来鹏措.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/等你在草原-阿桑·格来鹏措.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/红红的萨日朗-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/红红的萨日朗-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/纳西情歌-陈思思.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/纳西情歌-陈思思.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/美丽姑娘卓玛啦-央金.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/美丽姑娘卓玛啦-央金.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/羞答答的玫瑰唱情歌-格格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/羞答答的玫瑰唱情歌-格格.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/花儿香-春雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/花儿香-春雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原一枝花-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原一枝花-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原之恋-泽旺多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原之恋-泽旺多吉.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原之月-乌兰托娅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原之月-乌兰托娅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原夜色美-德德玛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原夜色美-德德玛.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原夜色美-降央卓玛.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原夜色美-降央卓玛.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原姑娘-泽尔丹.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原姑娘-泽尔丹.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原恋歌-兰卡措.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原恋歌-兰卡措.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原情哥哥-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原情哥哥-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原情郎-康姆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原情郎-康姆.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原情郎-朱贝贝.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原情郎-朱贝贝.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原春天美-泽旺多吉.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原春天美-泽旺多吉.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原百灵-央金.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原百灵-央金.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原的月亮-云飞.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原的月亮-云飞.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原绿了-兰卡措.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原绿了-兰卡措.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原舞曲-张冬玲.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原舞曲-张冬玲.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原请你来-蒙克.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原请你来-蒙克.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/草原醉-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/草原醉-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/蓝色故乡-四郎曲珍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/蓝色故乡-四郎曲珍.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/西藏情歌-格格.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/西藏情歌-格格.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/请到草原来-呼斯楞.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/请到草原来-呼斯楞.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/谁的帐篷-小骆驼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/谁的帐篷-小骆驼.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/走进草原-游美灵.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/走进草原-游美灵.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/这是我的家乡-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/这是我的家乡-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/遇上你是我的缘-央金兰泽.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/遇上你是我的缘-央金兰泽.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/那溪那山-甘雅丹.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/那溪那山-甘雅丹.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/醉美草原-乌兰河.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/醉美草原-乌兰河.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/金色的草原-群星.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/金色的草原-群星.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/陪你一起看草原-张薇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/陪你一起看草原-张薇.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/陪你一起看草原-月亮.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/陪你一起看草原-月亮.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/雪山姑娘-巴桑拉姆.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/雪山姑娘-巴桑拉姆.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/雪山姑娘-蒙克.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/雪山姑娘-蒙克.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/高原上的梦-阿斯根.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/高原上的梦-阿斯根.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/高原女人-央金兰泽.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/高原女人-央金兰泽.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/高原情-齐旦布.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/高原情-齐旦布.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/高原蓝-乌兰图雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/高原蓝-乌兰图雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/高原蓝-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/高原蓝-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/高原情-齐旦布.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/高原情-齐旦布.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/高原女人-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/高原女人-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/高原上的梦-阿斯根.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/高原上的梦-阿斯根.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/雪山姑娘-蒙克.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/雪山姑娘-蒙克.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/雪山姑娘-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/雪山姑娘-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/陪你一起看草原-月亮.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/陪你一起看草原-月亮.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/陪你一起看草原-张薇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/陪你一起看草原-张薇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/金色的草原-群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/金色的草原-群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/醉美草原-乌兰河.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/醉美草原-乌兰河.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/那溪那山-甘雅丹.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/那溪那山-甘雅丹.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/遇上你是我的缘-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/遇上你是我的缘-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/这是我的家乡-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/这是我的家乡-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/走进草原-游美灵.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/走进草原-游美灵.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/谁的帐篷-小骆驼.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/谁的帐篷-小骆驼.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/请到草原来-呼斯楞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/请到草原来-呼斯楞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/西藏情歌-格格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/西藏情歌-格格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/蓝色故乡-四郎曲珍.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/蓝色故乡-四郎曲珍.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原醉-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原醉-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原请你来-蒙克.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原请你来-蒙克.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原舞曲-张冬玲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原舞曲-张冬玲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原绿了-兰卡措.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原绿了-兰卡措.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原的月亮-云飞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原的月亮-云飞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原百灵-央金.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原百灵-央金.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原春天美-泽旺多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原春天美-泽旺多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原情郎-朱贝贝.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原情郎-朱贝贝.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原情郎-康姆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原情郎-康姆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原情哥哥-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原情哥哥-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原恋歌-兰卡措.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原恋歌-兰卡措.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原姑娘-泽尔丹.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原姑娘-泽尔丹.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原夜色美-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原夜色美-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原夜色美-德德玛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原夜色美-德德玛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原之月-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原之月-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原之恋-泽旺多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原之恋-泽旺多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/草原一枝花-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/草原一枝花-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/花儿香-春雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/花儿香-春雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/羞答答的玫瑰唱情歌-格格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/羞答答的玫瑰唱情歌-格格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/美丽姑娘卓玛啦-央金.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/美丽姑娘卓玛啦-央金.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/纳西情歌-陈思思.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/纳西情歌-陈思思.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/红红的萨日朗-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/红红的萨日朗-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/等你在草原-阿桑·格来鹏措.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/等你在草原-阿桑·格来鹏措.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/离别草原-云飞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/离别草原-云飞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/神奇的九寨-容中尔甲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/神奇的九寨-容中尔甲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/牧羊姑娘 (平四版)-华语群星.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/牧羊姑娘 (平四版)-华语群星.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/牧人-追风骏马组合.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/牧人-追风骏马组合.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/爱情海-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/爱情海-央金兰泽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/爱情在草原-夏兰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/爱情在草原-夏兰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/爱从草原来-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/爱从草原来-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/火辣辣的情歌-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/火辣辣的情歌-乌兰图雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/火苗-格格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/火苗-格格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/火红的萨日朗-要不要买菜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/火红的萨日朗-要不要买菜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/火红的萨日朗-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/火红的萨日朗-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/溜溜的姑娘像朵花-龚玥.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/溜溜的姑娘像朵花-龚玥.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/游牧情歌 (DJ版)-何鹏&格格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/游牧情歌 (DJ版)-何鹏&格格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/流浪的牧人-索朗扎西.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/流浪的牧人-索朗扎西.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/歌在飞-苏勒亚其其格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/歌在飞-苏勒亚其其格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/梦回云南 (DJ小鱼儿版)-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/梦回云南 (DJ小鱼儿版)-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/梦中的雪莲花-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/梦中的雪莲花-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/格桑拉-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/格桑拉-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/月光洒在草原上-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/月光洒在草原上-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/月亮上的姑娘-三郎王青.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/月亮上的姑娘-三郎王青.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/最炫民族风-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/最炫民族风-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/敖包相会-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/敖包相会-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/接新娘-皆大欢喜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/接新娘-皆大欢喜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/手捧奶酒敬亲人-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/手捧奶酒敬亲人-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我要去西藏-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我要去西藏-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我的草原我的缘-司徒兰芳.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我的草原我的缘-司徒兰芳.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我的根在草原-德德玛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我的根在草原-德德玛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我的根在草原-哈布尔.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我的根在草原-哈布尔.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我的新娘在草原-荣联合.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我的新娘在草原-荣联合.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我的九寨-泽尔丹.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我的九寨-泽尔丹.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我和草原有个约定-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我和草原有个约定-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我从草原来-萨顶顶.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我从草原来-萨顶顶.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/我从草原来-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/我从草原来-凤凰传奇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/想你想到草绿了-小颖.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/想你想到草绿了-小颖.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/心花开在草原上-吉娜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/心花开在草原上-吉娜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/心中的高原-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/心中的高原-降央卓玛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/彩云之南-徐千雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/彩云之南-徐千雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/家乡的牧场-蓝琪儿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/家乡的牧场-蓝琪儿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/姑娘我爱你-索朗扎西.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/姑娘我爱你-索朗扎西.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/姑娘我爱你-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/姑娘我爱你-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/套马杆-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/套马杆-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/天籁之爱-容中尔甲&旺姆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/天籁之爱-容中尔甲&旺姆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/天涯情歌-夏兰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/天涯情歌-夏兰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/天下最美的草原-呼斯楞.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/天下最美的草原-呼斯楞.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/天下最美-格格.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/天下最美-格格.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/天上西藏-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/天上西藏-白玛多吉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/大高原-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/大高原-乌兰托娅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/大草原-四海吉声文化.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/大草原-四海吉声文化.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/坐上火车去拉萨-徐千雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/坐上火车去拉萨-徐千雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/圣洁的西藏-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/圣洁的西藏-巴桑拉姆.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/唱首情歌给草原 (环绕声效)-罗海英.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/唱首情歌给草原 (环绕声效)-罗海英.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/唐古拉-王麟&完玛三智.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/唐古拉-王麟&完玛三智.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/吉祥赞-郭彦华.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/吉祥赞-郭彦华.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/又见高原红-容中尔甲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/又见高原红-容中尔甲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/卓玛-亚东.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/卓玛-亚东.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/草原歌曲/@eaDir/为你等待 (藏语版)-次仁央宗.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/草原歌曲/@eaDir/为你等待 (藏语版)-次仁央宗.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/心里愿望-未知歌手[aq].lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/心里愿望-未知歌手[aq].lrc"}, "/Volumes/music/虾米网易云/专辑集/翻唱/摩登兄弟刘宇宁 - 讲真的 (Live片段).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/摩登兄弟刘宇宁 - 讲真的 (Live片段).lrc"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-花火[weiyun].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-花火[weiyun].mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢活着viva_hydeMacBook-Pro_Feb-25-210207-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢活着viva_hydeMacBook-Pro_Feb-25-210207-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢活着viva.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢活着viva.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢圣斗士星矢主题曲2010(粤语版_正式版).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢圣斗士星矢主题曲2010(粤语版_正式版).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-黑色毛衣.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-黑色毛衣.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-黑夜不再来.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-黑夜不再来.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-饿狼传说(求hi其版).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-饿狼传说(求hi其版).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-阴天.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-阴天.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-这一次意外_hydeMacBook-Pro_Feb-25-210206-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-这一次意外_hydeMacBook-Pro_Feb-25-210206-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-这一次意外.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-这一次意外.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-葡萄成熟时.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-葡萄成熟时.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-花火.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-花火.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-祝君好.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-祝君好.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-祝你生日快乐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-祝你生日快乐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-真的爱你.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-真的爱你.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-男儿当入樽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-男儿当入樽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-王子的新衣.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-王子的新衣.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-爱下去.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-爱下去.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-浮夸.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-浮夸.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-洋葱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-洋葱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-枫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-枫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-我还想她.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-我还想她.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-心中的日月_hydeMacBook-Pro_Feb-25-210206-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-心中的日月_hydeMacBook-Pro_Feb-25-210206-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-心中的日月.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-心中的日月.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-大开眼戒.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-大开眼戒.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-坏坏惹人爱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-坏坏惹人爱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-唯一.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-唯一.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-命硬.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-命硬.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-告别的时代.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-告别的时代.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-十面埋伏.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-十面埋伏.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-光辉岁月(remix).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-光辉岁月(remix).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-借口.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-借口.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-你说的都对.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-你说的都对.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-从开始到现在.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-从开始到现在.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-不共戴天.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-不共戴天.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-七百年后.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-七百年后.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-you raise me up_永不沉没(祝福灾区人民).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-you raise me up_永不沉没(祝福灾区人民).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-you are not alone.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-you are not alone.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-smells like teen spirit.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-smells like teen spirit.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-missing you.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-missing you.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-She＇s gone.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-She＇s gone.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-1874.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/常愚蠢-1874.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/少年（常愚蠢_士巴那）.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/少年（常愚蠢_士巴那）.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-花火[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-花火[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/常愚蠢/@eaDir/唱吧 - 常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/青春纪念册-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/青春纪念册-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/闪灵-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/闪灵-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/遥远的她-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/遥远的她-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/这一次意外.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/这一次意外.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/讲不出再见-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/讲不出再见-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/苦情人-loveyez1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/苦情人-loveyez1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/耿耿于怀-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/耿耿于怀-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/给自己的情书-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/给自己的情书-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/终身美丽【道】-loveyez1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/终身美丽【道】-loveyez1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/笑看风云-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/笑看风云-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/知足-loveyez7.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/知足-loveyez7.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/眷恋-loveyez4.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/眷恋-loveyez4.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/真真假假-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/真真假假-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/相逢在雨中-loveyez9.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/相逢在雨中-loveyez9.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/相爱很难-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/相爱很难-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/目黑-loveyez6.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/目黑-loveyez6.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/白玫瑰-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/白玫瑰-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/玫瑰花的葬礼-loveyez2.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/玫瑰花的葬礼-loveyez2.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/热血燃烧-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/热血燃烧-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/沉默是金【cy名、贵】-loveyez3.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/沉默是金【cy名、贵】-loveyez3.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/永远爱着你-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/永远爱着你-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/死性不改-loveyez9.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/死性不改-loveyez9.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/月光传说-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/月光传说-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/月下祷告-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/月下祷告-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/晚秋-loveyez9.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/晚秋-loveyez9.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/春风十里-loveyez5.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/春风十里-loveyez5.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/明年今日-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/明年今日-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/无间道-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/无间道-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/我的回忆不是我的-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/我的回忆不是我的-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/忘情冷雨夜-loveyez0.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/忘情冷雨夜-loveyez0.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/必杀技1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/必杀技1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/必杀技.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/必杀技.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/必杀技-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/必杀技-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/当爱在靠近-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/当爱在靠近-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/开始恋爱-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/开始恋爱-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/少女的祈祷-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/少女的祈祷-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/富士山下-loveyez6.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/富士山下-loveyez6.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/好姑娘-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/好姑娘-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/天涯孤客-loveyez8.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/天涯孤客-loveyez8.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/够钟-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/够钟-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/国境之南.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/国境之南.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/吻得到爱不到-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/吻得到爱不到-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/再见亦是泪-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/再见亦是泪-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/做你的男人-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/做你的男人-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/假装-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/假装-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/习惯失恋.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/习惯失恋.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/下学期.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/下学期.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/下学期-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/下学期-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/下学期(1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/下学期(1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/上海滩-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/上海滩-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/三角志-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/三角志-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/七友.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/七友.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/七友-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/七友-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/loveyez/@eaDir/1937-loveyez.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/loveyez/@eaDir/1937-loveyez.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你的姓氏我的名字-tsgx.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你的姓氏我的名字-tsgx.mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你的姓氏我的名字.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你的姓氏我的名字.mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/唱吧 - 常愚蠢-my love fate.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/唱吧 - 常愚蠢-my love fate.mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇妙能力歌-梦遗少年.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇妙能力歌-梦遗少年.mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇洛李维斯回信-Mr朗尼2[weiyun].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇洛李维斯回信-Mr朗尼2[weiyun].mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇洛李维斯回信-顾船长2[weiyun].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇洛李维斯回信-顾船长2[weiyun].mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/斑马斑马-航0[weiyun].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/斑马斑马-航0[weiyun].mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/断了的弦-hata.mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/断了的弦-hata.mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/长路漫漫伴我闯-生果牌先生[weiyun].mp3@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/长路漫漫伴我闯-生果牌先生[weiyun].mp3@SynoResource"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/黄海狸（MOKONA010补档处 give a reason.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/黄海狸（MOKONA010补档处 give a reason.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/魂游太虚.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/魂游太虚.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/马上英姿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/马上英姿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/飘雪-lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/飘雪-lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/雨蝶.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/雨蝶.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/雨蝶-yusuki-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/雨蝶-yusuki-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/阿拉灯 片头 这位好友真不错 -陶赞新.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/阿拉灯 片头 这位好友真不错 -陶赞新.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/长路漫漫伴我闯-生果牌先生[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/长路漫漫伴我闯-生果牌先生[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/酒红色的心.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/酒红色的心.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/酒红色的心-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/酒红色的心-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/那美克星一根草 zenki(鬼神童子OP).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/那美克星一根草 zenki(鬼神童子OP).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/那美克星一根草 breeze.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/那美克星一根草 breeze.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/那些年-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/那些年-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/这一次意外.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/这一次意外.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/谁明浪子心.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/谁明浪子心.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/请继续，任性-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/请继续，任性-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/让一切随风-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/让一切随风-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/裙下之臣.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/裙下之臣.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/蜚蜚.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/蜚蜚.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/苦情人.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/苦情人.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/苦情人-loveyez1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/苦情人-loveyez1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/芳华绝代-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/芳华绝代-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/艾唯 邪魔はさせない.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/艾唯 邪魔はさせない.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/良佳公子~ 我只在乎你.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/良佳公子~ 我只在乎你.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/自作多情.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/自作多情.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/老鼠爱大米.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/老鼠爱大米.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/红玫瑰-Luovi8.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/红玫瑰-Luovi8.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/等不到的爱-龙卷风.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/等不到的爱-龙卷风.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/第17集【合唱挑战】一人一句唱《富士山下》 - 小红书_video.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/第17集【合唱挑战】一人一句唱《富士山下》 - 小红书_video.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/真真假假(1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/真真假假(1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/看我七十二变-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/看我七十二变-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/目黑-Moon1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/目黑-Moon1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/盛夏的果实-桐哥哥耶7.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/盛夏的果实-桐哥哥耶7.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/盛夏的果实-桐哥哥耶7-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/盛夏的果实-桐哥哥耶7-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/皇家双妹唛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/皇家双妹唛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/白玫瑰-崩溃的豆腐8_hydeMacBook-Pro_Feb-25-210152-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/白玫瑰-崩溃的豆腐8_hydeMacBook-Pro_Feb-25-210152-2021_Conflict.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/白玫瑰-崩溃的豆腐8.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/白玫瑰-崩溃的豆腐8.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/白玫瑰-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/白玫瑰-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/玩玩具.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/玩玩具.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/王子的新衣-龙卷风.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/王子的新衣-龙卷风.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/爱就爱了.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/爱就爱了.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/漫步在日本爱知县名古屋市现代社区.mp4/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/漫步在日本爱知县名古屋市现代社区.mp4/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/演员-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/演员-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/演员-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/演员-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/浮夸-ring.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/浮夸-ring.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/流着泪说分手.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/流着泪说分手.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/流着泪说分手-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/流着泪说分手-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/泡沫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/泡沫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/没那么简单-luovi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/没那么简单-luovi.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/沙漠骆驼-DeepFeel6-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/沙漠骆驼-DeepFeel6-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/残酷な天使のテーゼ-FrankieFu3.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/残酷な天使のテーゼ-FrankieFu3.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/森林.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/森林.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/枫影儿 - 青媚狐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/枫影儿 - 青媚狐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/李白-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/李白-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/期待.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/期待.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/朋友仔.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/朋友仔.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/有点甜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/有点甜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/暗里着迷-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/暗里着迷-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/晃儿 - 1937.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/晃儿 - 1937.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/春风十里-梦遺少年3.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/春风十里-梦遺少年3.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/春风十里-梦遺少年3-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/春风十里-梦遺少年3-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/星辰变【原版伴奏】-李雄2.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/星辰变【原版伴奏】-李雄2.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/星辰变-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/星辰变-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/明月天涯-梦遺少年2.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/明月天涯-梦遺少年2.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/断了的弦.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/断了的弦.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/断了的弦-hata.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/断了的弦-hata.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/斑马斑马-航0[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/斑马斑马-航0[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/摩登兄弟刘宇宁 - 讲真的 (Live片段).flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/摩登兄弟刘宇宁 - 讲真的 (Live片段).flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/摩登兄弟 - 我的秘密 (片段).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/摩登兄弟 - 我的秘密 (片段).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/我要你【驴得水电影主题曲】-Niki1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/我要你【驴得水电影主题曲】-Niki1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/我的回忆不是我的-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/我的回忆不是我的-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/我想我不够好.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/我想我不够好.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/想你想疯了.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/想你想疯了.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/惨得过我.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/惨得过我.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/患蛇精病の玖时源 幻化成风.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/患蛇精病の玖时源 幻化成风.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/患蛇精病の玖时源 勇气100%（打开天空).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/患蛇精病の玖时源 勇气100%（打开天空).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/恋之风景-粤【男版降调】-阿六Jad2.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/恋之风景-粤【男版降调】-阿六Jad2.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/恋之风景-粤【男版降调】-阿六Jad2-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/恋之风景-粤【男版降调】-阿六Jad2-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/怎么唱情歌.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/怎么唱情歌.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/心酸的情歌.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/心酸的情歌.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/开始恋爱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/开始恋爱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/干物女.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/干物女.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/帝女芳魂.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/帝女芳魂.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/帝女花.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/帝女花.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/少女的祈祷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/少女的祈祷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/小非★X-PRO☆ 白月光.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/小非★X-PRO☆ 白月光.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/小糖 - 苦情人.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/小糖 - 苦情人.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/小幸运-luovi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/小幸运-luovi.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/将军.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/将军.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/如果这都不算爱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/如果这都不算爱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/好想你【吉他伴奏】-粟冶1.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/好想你【吉他伴奏】-粟冶1.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/好想你-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/好想你-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/好想你-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/好想你-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/好想你-文文.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/好想你-文文.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/好好恋爱-lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/好好恋爱-lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇然 樱花零乱.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇然 樱花零乱.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇然 - 青媚狐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇然 - 青媚狐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇洛李维斯的回信.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇洛李维斯的回信.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇洛李维斯回信-顾船长2[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇洛李维斯回信-顾船长2[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇洛李维斯回信-Mr朗尼2[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇洛李维斯回信-Mr朗尼2[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/奇妙能力歌-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/奇妙能力歌-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/头发乱了.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/头发乱了.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/天长地久-宋小鸡9.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/天长地久-宋小鸡9.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/天台的月光.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/天台的月光.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/大笑江湖-翻唱作品.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/大笑江湖-翻唱作品.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/够钟-ring.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/够钟-ring.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/夜曲【无损版伴奏】-DeepFeel1-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/夜曲【无损版伴奏】-DeepFeel1-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/回忆的沙漏.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/回忆的沙漏.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/喜帖街.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/喜帖街.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/唱吧 - 常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/唱吧 - 常愚蠢-my love fate.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/听风的歌.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/听风的歌.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/同舟之情.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/同舟之情.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/去大理-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/去大理-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/十个女仔.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/十个女仔.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/刚刚好-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/刚刚好-梦遗少年-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/再見我的初戀【男版】-大頭6.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/再見我的初戀【男版】-大頭6.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/兔兔Get along.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/兔兔Get along.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/值得-CAROLIN63-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/值得-CAROLIN63-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你若成风.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你若成风.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你的姓氏我的名字.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你的姓氏我的名字.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你的姓氏我的名字-tsgx.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你的姓氏我的名字-tsgx.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你爱我像谁-大宸宸2.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你爱我像谁-大宸宸2.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你爱我像谁-大宸宸2-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你爱我像谁-大宸宸2-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/你你你为了爱情.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/你你你为了爱情.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/伤感的恋人.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/伤感的恋人.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/今夜你会不会来.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/今夜你会不会来.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/井.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/井.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/为你钟情.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/为你钟情.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/丸子didiwan 说爱你.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/丸子didiwan 说爱你.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/丸子didiwan  give a reason.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/丸子didiwan  give a reason.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/不搭-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/不搭-梦遗少年.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/下学期-花轮.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/下学期-花轮.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/一百块都不给我.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/一百块都不给我.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/一百块都不给我-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/一百块都不给我-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/一事无成.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/一事无成.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/※聖音※友吧小Y（歪蛋蛋就是我）  小城大事-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/※聖音※友吧小Y（歪蛋蛋就是我）  小城大事-未知歌手[aq].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/tsol 至少还有你-未知歌手[aq](1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/tsol 至少还有你-未知歌手[aq](1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/fly me to the monn.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/fly me to the monn.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/diary.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/diary.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/dear friends-和风蜘蛛.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/dear friends-和风蜘蛛.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/breeze.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/breeze.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/brave heart.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/brave heart.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/Bohemian Rhapsody.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/Bohemian Rhapsody.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/6月19日 (1)(1).mp4/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/6月19日 (1)(1).mp4/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/4K风光美景治愈系.mp4/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/4K风光美景治愈系.mp4/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/16号爱人-Lee[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/16号爱人-<PERSON>[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/16号爱人-Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/16号爱人-Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/翻唱/@eaDir/- 一百块都不给我[weiyun].mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/翻唱/@eaDir/- 一百块都不给我[weiyun].mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/01胡广生-《无名之辈》电影宣传推广曲 - 任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/01胡广生-《无名之辈》电影宣传推广曲 - 任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/02王招君 (你看你拉住我的模样)-《寻汉计》电影推广曲 - 任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/02王招君 (你看你拉住我的模样)-《寻汉计》电影推广曲 - 任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/030028下一站茶山刘-房东的猫&安来宁 - 大白兔姗珊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/030028下一站茶山刘-房东的猫&安来宁 - 大白兔姗珊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/04南方 南方 - 张小九.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/04南方 南方 - 张小九.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/05南风北巷 - 邵帅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/05南风北巷 - 邵帅.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/06启程 - 水木年华&房东的猫.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/06启程 - 水木年华&房东的猫.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/07碎银几两 - 轩东.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/07碎银几两 - 轩东.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/08我要你-《驴得水》电影主题曲 - 任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/08我要你-《驴得水》电影主题曲 - 任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/09想把我唱给你听 - 李家鑫&张茜.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/09想把我唱给你听 - 李家鑫&张茜.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/100云烟成雨-《我是江小白》动画片尾曲 - 房东的猫.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/100云烟成雨-《我是江小白》动画片尾曲 - 房东的猫.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/10幸福的两口子-《福根进城》片尾曲《幸福的两口子》电视剧主题曲 - 庞龙.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/10幸福的两口子-《福根进城》片尾曲《幸福的两口子》电视剧主题曲 - 庞龙.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/11光阴的故事 - 孙露&杨波.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/11光阴的故事 - 孙露&杨波.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/12可能否（新版） - 木小雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/12可能否（新版） - 木小雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/13你是人间四月天 - 邵帅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/13你是人间四月天 - 邵帅.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/14七月上 - Jam.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/14七月上 - Jam.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/15岁岁-《故乡，别来无恙》电视剧重逢曲 - 任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/15岁岁-《故乡，别来无恙》电视剧重逢曲 - 任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/16我们的时光 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/16我们的时光 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/17中华民谣 - 孙浩.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/17中华民谣 - 孙浩.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/18白羊 - 徐秉龙&沈以诚.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/18白羊 - 徐秉龙&沈以诚.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/19半生 - 张磊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/19半生 - 张磊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/20朝阳花开 - 高进.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/20朝阳花开 - 高进.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/21归去来兮 - 花粥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/21归去来兮 - 花粥.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/22漠河舞厅 - 戴羽彤.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/22漠河舞厅 - 戴羽彤.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/23蚍蜉 - 花粥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/23蚍蜉 - 花粥.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/24随风去吧 - 海超.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/24随风去吧 - 海超.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/25大波浪姑娘 - 王荻.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/25大波浪姑娘 - 王荻.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/26多情种 - 徐泽（要不要买菜）.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/26多情种 - 徐泽（要不要买菜）.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/27南方姑娘-《米花之味》电影推广曲 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/27南方姑娘-《米花之味》电影推广曲 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/28你的姑娘 - 隔壁老樊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/28你的姑娘 - 隔壁老樊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/29你会不会想起我 - 杨瑜婷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/29你会不会想起我 - 杨瑜婷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/30少年锦时 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/30少年锦时 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/31同桌的你 - 老狼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/31同桌的你 - 老狼.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/32我要你  - 老狼&任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/32我要你  - 老狼&任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/33鼓楼 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/33鼓楼 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/34画 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/34画 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/35老鹰之歌 - 岩贵.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/35老鹰之歌 - 岩贵.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/36那个女孩 - 李耀阳.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/36那个女孩 - 李耀阳.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/37桥边姑娘 - 安小木.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/37桥边姑娘 - 安小木.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/38我问月亮啊月亮 - 全幺九.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/38我问月亮啊月亮 - 全幺九.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/39小镇姑娘 - 李瑨瑶.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/39小镇姑娘 - 李瑨瑶.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/40出山 - 花粥&王胜娚.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/40出山 - 花粥&王胜娚.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/41多想在平庸的生活拥抱你 - 隔壁老樊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/41多想在平庸的生活拥抱你 - 隔壁老樊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/42故乡 - 许巍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/42故乡 - 许巍.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/43空空-我是唱作人2第2期 - 陈粒.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/43空空-我是唱作人2第2期 - 陈粒.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/44凌晨计程车 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/44凌晨计程车 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/45去大理-《心花路放》电影插曲 - 郝云.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/45去大理-《心花路放》电影插曲 - 郝云.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/46我记得 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/46我记得 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/47我在等你 - 沐乐团.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/47我在等你 - 沐乐团.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/48不露声色 - Jam.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/48不露声色 - Jam.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/49失乐 - 隔壁老樊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/49失乐 - 隔壁老樊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/50我不是你的宋冬野 - 刘大壮.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/50我不是你的宋冬野 - 刘大壮.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/51像我这样的人 (with 毛不易) - 鹿晗&毛不易.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/51像我这样的人 (with 毛不易) - 鹿晗&毛不易.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/52小行迹 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/52小行迹 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/53遥不可及的你 - 花粥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/53遥不可及的你 - 花粥.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/54儿时 - 刘昊霖.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/54儿时 - 刘昊霖.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/55二十岁的某一天 - 花粥.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/55二十岁的某一天 - 花粥.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/56父亲的散文诗 - 徐俊雅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/56父亲的散文诗 - 徐俊雅.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/57玫瑰 - 贰佰.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/57玫瑰 - 贰佰.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/58送你一朵小红花-《送你一朵小红花》电影主题曲 - 赵英俊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/58送你一朵小红花-《送你一朵小红花》电影主题曲 - 赵英俊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/59写给黄淮 - 邵帅.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/59写给黄淮 - 邵帅.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/60在他乡 - 七叔（叶泽浩）.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/60在他乡 - 七叔（叶泽浩）.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/61歌曲：枕着光的她 - 任素汐.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/61歌曲：枕着光的她 - 任素汐.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/62归来-选择导师：羽泉 - 满江.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/62归来-选择导师：羽泉 - 满江.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/63玛丽 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/63玛丽 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/64南山南 - 马頔.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/64南山南 - 马頔.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/65我把我的理想卖了 - 二立.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/65我把我的理想卖了 - 二立.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/66我想当风-《抓娃娃》电影片尾曲 - 鹿先森乐队.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/66我想当风-《抓娃娃》电影片尾曲 - 鹿先森乐队.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/67远方有信仰 - 海来阿木.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/67远方有信仰 - 海来阿木.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/68爱上你我很快乐 - 水木年华.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/68爱上你我很快乐 - 水木年华.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/69程艾影 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/69程艾影 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/70朵 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/70朵 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/71姬和不如 - 隔壁老樊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/71姬和不如 - 隔壁老樊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/72十九岁 - 赵雷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/72十九岁 - 赵雷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/73探清水河 - 浅影阿.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/73探清水河 - 浅影阿.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/74成都 - 雷婷.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/74成都 - 雷婷.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/75村里的春 - 春风绣.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/75村里的春 - 春风绣.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/76丁香花 - 七元.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/76丁香花 - 七元.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/77可乐 - 陈小虎.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/77可乐 - 陈小虎.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/78梦伴 - 李悦君.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/78梦伴 - 李悦君.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/79完美生活 - 许巍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/79完美生活 - 许巍.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/80知道不知道 - 侃侃.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/80知道不知道 - 侃侃.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/81不找了 - 郭旭.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/81不找了 - 郭旭.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/82凉州词 - 苏阳.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/82凉州词 - 苏阳.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/83情人劫 - 老狼.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/83情人劫 - 老狼.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/84四块五 - 隔壁老樊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/84四块五 - 隔壁老樊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/85像风一样自由 - 许巍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/85像风一样自由 - 许巍.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/86小黄花 - 陕西牛犇.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/86小黄花 - 陕西牛犇.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/87小宇 - 蓝心羽.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/87小宇 - 蓝心羽.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/88这一生关于你的风景 - 枯木逢春.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/88这一生关于你的风景 - 枯木逢春.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/89Sleepyhead - Galen Crew.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/89Sleepyhead - Galen Crew.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/90We Can&#39;t Stop - Boyce Avenue&Bea Miller.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/90We Can&#39;t Stop - Boyce Avenue&Bea Miller.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/91风吹麦浪-《女人的战争》电视剧片尾曲 - 李健.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/91风吹麦浪-《女人的战争》电视剧片尾曲 - 李健.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/92童年-《夏至未至》电视剧插曲 - 罗大佑.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/92童年-《夏至未至》电视剧插曲 - 罗大佑.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/93我的爱-《觉醒》慕思视频主题曲 - 许巍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/93我的爱-《觉醒》慕思视频主题曲 - 许巍.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/94向往 - 李健.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/94向往 - 李健.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/95丁香花 - 唐磊.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/95丁香花 - 唐磊.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/96记念 - RAiNBOW计划&雷雨心.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/96记念 - RAiNBOW计划&雷雨心.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/97蓝莲花 - 许巍.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/97蓝莲花 - 许巍.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/98听说 - 丛铭君.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/98听说 - 丛铭君.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/99一滴泪的时间 - 赵紫骅&斑马森林.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/99一滴泪的时间 - 赵紫骅&斑马森林.lrc"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/99一滴泪的时间 - 赵紫骅&斑马森林.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/99一滴泪的时间 - 赵紫骅&斑马森林.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/98听说 - 丛铭君.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/98听说 - 丛铭君.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/97蓝莲花 - 许巍.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/97蓝莲花 - 许巍.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/96记念 - RAiNBOW计划&雷雨心.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/96记念 - RAiNBOW计划&雷雨心.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/95丁香花 - 唐磊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/95丁香花 - 唐磊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/94向往 - 李健.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/94向往 - 李健.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/93我的爱-《觉醒》慕思视频主题曲 - 许巍.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/93我的爱-《觉醒》慕思视频主题曲 - 许巍.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/92童年-《夏至未至》电视剧插曲 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/92童年-《夏至未至》电视剧插曲 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/91风吹麦浪-《女人的战争》电视剧片尾曲 - 李健.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/91风吹麦浪-《女人的战争》电视剧片尾曲 - 李健.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/90We Can&#39;t Stop - Boyce Avenue&Bea Miller.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/90We Can&#39;t Stop - Boyce Avenue&Bea Miller.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/89Sleepyhead - Galen Crew.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/89Sleepyhead - Galen Crew.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/88这一生关于你的风景 - 枯木逢春.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/88这一生关于你的风景 - 枯木逢春.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/87小宇 - 蓝心羽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/87小宇 - 蓝心羽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/86小黄花 - 陕西牛犇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/86小黄花 - 陕西牛犇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/84四块五 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/84四块五 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/83情人劫 - 老狼.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/83情人劫 - 老狼.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/82凉州词 - 苏阳.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/82凉州词 - 苏阳.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/81不找了 - 郭旭.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/81不找了 - 郭旭.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/80知道不知道 - 侃侃.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/80知道不知道 - 侃侃.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/79完美生活 - 许巍.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/79完美生活 - 许巍.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/78梦伴 - 李悦君.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/78梦伴 - 李悦君.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/77可乐 - 陈小虎.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/77可乐 - 陈小虎.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/76丁香花 - 七元.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/76丁香花 - 七元.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/75村里的春 - 春风绣.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/75村里的春 - 春风绣.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/74成都 - 雷婷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/74成都 - 雷婷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/73探清水河 - 浅影阿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/73探清水河 - 浅影阿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/72十九岁 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/72十九岁 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/71姬和不如 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/71姬和不如 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/70朵 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/70朵 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/69程艾影 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/69程艾影 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/68爱上你我很快乐 - 水木年华.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/68爱上你我很快乐 - 水木年华.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/67远方有信仰 - 海来阿木.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/67远方有信仰 - 海来阿木.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/66我想当风-《抓娃娃》电影片尾曲 - 鹿先森乐队.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/66我想当风-《抓娃娃》电影片尾曲 - 鹿先森乐队.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/65我把我的理想卖了 - 二立.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/65我把我的理想卖了 - 二立.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/63玛丽 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/63玛丽 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/62归来-选择导师：羽泉 - 满江.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/62归来-选择导师：羽泉 - 满江.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/61歌曲：枕着光的她 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/61歌曲：枕着光的她 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/60在他乡 - 七叔（叶泽浩）.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/60在他乡 - 七叔（叶泽浩）.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/59写给黄淮 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/59写给黄淮 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/58送你一朵小红花-《送你一朵小红花》电影主题曲 - 赵英俊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/58送你一朵小红花-《送你一朵小红花》电影主题曲 - 赵英俊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/57玫瑰 - 贰佰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/57玫瑰 - 贰佰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/56父亲的散文诗 - 徐俊雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/56父亲的散文诗 - 徐俊雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/55二十岁的某一天 - 花粥.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/55二十岁的某一天 - 花粥.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/54儿时 - 刘昊霖.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/54儿时 - 刘昊霖.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/53遥不可及的你 - 花粥.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/53遥不可及的你 - 花粥.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/52小行迹 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/52小行迹 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/51像我这样的人 (with 毛不易) - 鹿晗&毛不易.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/51像我这样的人 (with 毛不易) - 鹿晗&毛不易.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/50我不是你的宋冬野 - 刘大壮.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/50我不是你的宋冬野 - 刘大壮.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/49失乐 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/49失乐 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/48不露声色 - Jam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/48不露声色 - Jam.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/47我在等你 - 沐乐团.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/47我在等你 - 沐乐团.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/46我记得 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/46我记得 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/45去大理-《心花路放》电影插曲 - 郝云.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/45去大理-《心花路放》电影插曲 - 郝云.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/44凌晨计程车 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/44凌晨计程车 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/43空空-我是唱作人2第2期 - 陈粒.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/43空空-我是唱作人2第2期 - 陈粒.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/42故乡 - 许巍.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/42故乡 - 许巍.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/41多想在平庸的生活拥抱你 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/41多想在平庸的生活拥抱你 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/40出山 - 花粥&王胜娚.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/40出山 - 花粥&王胜娚.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/39小镇姑娘 - 李瑨瑶.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/39小镇姑娘 - 李瑨瑶.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/38我问月亮啊月亮 - 全幺九.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/38我问月亮啊月亮 - 全幺九.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/37桥边姑娘 - 安小木.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/37桥边姑娘 - 安小木.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/36那个女孩 - 李耀阳.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/36那个女孩 - 李耀阳.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/35老鹰之歌 - 岩贵.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/35老鹰之歌 - 岩贵.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/34画 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/34画 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/33鼓楼 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/33鼓楼 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/32我要你  - 老狼&任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/32我要你  - 老狼&任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/31同桌的你 - 老狼.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/31同桌的你 - 老狼.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/30少年锦时 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/30少年锦时 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/29你会不会想起我 - 杨瑜婷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/29你会不会想起我 - 杨瑜婷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/28你的姑娘 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/28你的姑娘 - 隔壁老樊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/27南方姑娘-《米花之味》电影推广曲 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/27南方姑娘-《米花之味》电影推广曲 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/26多情种 - 徐泽（要不要买菜）.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/26多情种 - 徐泽（要不要买菜）.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/25大波浪姑娘 - 王荻.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/25大波浪姑娘 - 王荻.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/24随风去吧 - 海超.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/24随风去吧 - 海超.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/23蚍蜉 - 花粥.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/23蚍蜉 - 花粥.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/22漠河舞厅 - 戴羽彤.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/22漠河舞厅 - 戴羽彤.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/21归去来兮 - 花粥.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/21归去来兮 - 花粥.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/20朝阳花开 - 高进.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/20朝阳花开 - 高进.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/19半生 - 张磊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/19半生 - 张磊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/18白羊 - 徐秉龙&沈以诚.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/18白羊 - 徐秉龙&沈以诚.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/17中华民谣 - 孙浩.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/17中华民谣 - 孙浩.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/16我们的时光 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/16我们的时光 - 赵雷.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/15岁岁-《故乡，别来无恙》电视剧重逢曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/15岁岁-《故乡，别来无恙》电视剧重逢曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/14七月上 - Jam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/14七月上 - Jam.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/13你是人间四月天 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/13你是人间四月天 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/12可能否（新版） - 木小雅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/12可能否（新版） - 木小雅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/11光阴的故事 - 孙露&杨波.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/11光阴的故事 - 孙露&杨波.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/10幸福的两口子-《福根进城》片尾曲《幸福的两口子》电视剧主题曲 - 庞龙.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/10幸福的两口子-《福根进城》片尾曲《幸福的两口子》电视剧主题曲 - 庞龙.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/100云烟成雨-《我是江小白》动画片尾曲 - 房东的猫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/100云烟成雨-《我是江小白》动画片尾曲 - 房东的猫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/09想把我唱给你听 - 李家鑫&张茜.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/09想把我唱给你听 - 李家鑫&张茜.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/08我要你-《驴得水》电影主题曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/08我要你-《驴得水》电影主题曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/07碎银几两 - 轩东.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/07碎银几两 - 轩东.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/06启程 - 水木年华&房东的猫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/06启程 - 水木年华&房东的猫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/05南风北巷 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/05南风北巷 - 邵帅.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/04南方 南方 - 张小九.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/04南方 南方 - 张小九.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/030028下一站茶山刘-房东的猫&安来宁 - 大白兔姗珊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/030028下一站茶山刘-房东的猫&安来宁 - 大白兔姗珊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/02王招君 (你看你拉住我的模样)-《寻汉计》电影推广曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/02王招君 (你看你拉住我的模样)-《寻汉计》电影推广曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/民谣/@eaDir/01胡广生-《无名之辈》电影宣传推广曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/民谣/@eaDir/01胡广生-《无名之辈》电影宣传推广曲 - 任素汐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/100.孙露-鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/100.孙露-鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/099.郭峰-永远.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/099.郭峰-永远.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/098.吴奇隆-祝你一路顺风.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/098.吴奇隆-祝你一路顺风.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/097.刘嘉亮-你到底爱谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/097.刘嘉亮-你到底爱谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/096.林志颖-十七岁的雨季.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/096.林志颖-十七岁的雨季.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/095.陶喆-就是爱你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/095.陶喆-就是爱你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/094.黄凯芹-雨中的恋人们.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/094.黄凯芹-雨中的恋人们.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/093.In Love-一千零一个愿望.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/093.In Love-一千零一个愿望.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/092.张宇-月亮惹的祸.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/092.张宇-月亮惹的祸.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/091.薛之谦-演员.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/091.薛之谦-演员.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/090.李晓杰-朋友的酒.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/090.李晓杰-朋友的酒.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/089.李圣杰-痴心绝对.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/089.李圣杰-痴心绝对.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/088.裘海正-爱我的人和我爱的人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/088.裘海正-爱我的人和我爱的人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/087.容祖儿-小小.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/087.容祖儿-小小.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/085.许韶洋-花香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/085.许韶洋-花香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/084.周慧敏-最爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/084.周慧敏-最爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/083.林依轮-爱情鸟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/083.林依轮-爱情鸟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/082.五月天-突然好想你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/082.五月天-突然好想你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/081.郑源-一万个理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/081.郑源-一万个理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/080.无印良品-掌心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/080.无印良品-掌心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/079.动力火车-终于明白.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/079.动力火车-终于明白.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/078.旭日阳刚-怀念青春.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/078.旭日阳刚-怀念青春.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/077.信乐团-死了都要爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/077.信乐团-死了都要爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/075.邰正宵-想你想得好孤寂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/075.邰正宵-想你想得好孤寂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/074.高胜美-情难枕.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/074.高胜美-情难枕.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/073.张信哲-爱如潮水.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/073.张信哲-爱如潮水.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/072.邝美云-我和春天有个约会.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/072.邝美云-我和春天有个约会.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/070.庾澄庆-情非得已.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/070.庾澄庆-情非得已.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/069.毛宁-涛声依旧.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/069.毛宁-涛声依旧.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/068.羽·泉-彩虹.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/068.羽·泉-彩虹.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/067.谢安琪-钟无艳.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/067.谢安琪-钟无艳.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/066.张学友-一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/066.张学友-一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/065.永邦-你是我最深爱的人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/065.永邦-你是我最深爱的人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/064.沙宝亮-暗香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/064.沙宝亮-暗香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/063.小沈阳_高进-我的好兄弟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/063.小沈阳_高进-我的好兄弟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/062.张雨生-大海.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/062.张雨生-大海.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/061.杨宗纬-洋葱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/061.杨宗纬-洋葱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/060.王杰-为了爱梦一生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/060.王杰-为了爱梦一生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/059.张卫健-你爱我像谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/059.张卫健-你爱我像谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/058.林忆莲_李宗盛-当爱已成往事.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/058.林忆莲_李宗盛-当爱已成往事.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/057.李行亮-愿得一人心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/057.李行亮-愿得一人心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/056.侯湘婷-暧昧.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/056.侯湘婷-暧昧.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/055.林宥嘉-你是我的眼.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/055.林宥嘉-你是我的眼.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/054.黄龄-High歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/054.黄龄-High歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/053.张学友-慢慢.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/053.张学友-慢慢.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/052.孙楠-来世还要在一起.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/052.孙楠-来世还要在一起.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/051.张惠妹-我最亲爱的.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/051.张惠妹-我最亲爱的.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/050.关喆-想你的夜.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/050.关喆-想你的夜.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/049.张震岳-爱我别走.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/049.张震岳-爱我别走.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/048.赵传-我终于失去了你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/048.赵传-我终于失去了你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/047.田震-执着.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/047.田震-执着.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/046.柯以敏-河流.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/046.柯以敏-河流.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/045.彭羚-囚鸟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/045.彭羚-囚鸟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/044.陈晓东-我比谁都清楚.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/044.陈晓东-我比谁都清楚.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/043.辛晓琪-领悟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/043.辛晓琪-领悟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/042.成龙_金喜善-美丽的神话.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/042.成龙_金喜善-美丽的神话.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/041.何润东-没有我你怎么办.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/041.何润东-没有我你怎么办.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/040.杨林-玻璃心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/040.杨林-玻璃心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/039.ALL-RANGE-樱花草.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/039.ALL-RANGE-樱花草.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/038.谭咏麟-讲不出再见.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/038.谭咏麟-讲不出再见.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/037.李克勤-月半小夜曲.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/037.李克勤-月半小夜曲.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/036.徐怀钰-我是女生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/036.徐怀钰-我是女生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/035.黄品源-你怎么舍得我难过.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/035.黄品源-你怎么舍得我难过.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/034.汤潮-狼爱上羊.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/034.汤潮-狼爱上羊.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/033.吕方-老情歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/033.吕方-老情歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/032.迪克牛仔-有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/032.迪克牛仔-有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/031.伍思凯-特别的爱给特别的你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/031.伍思凯-特别的爱给特别的你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/030.周杰伦-七里香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/030.周杰伦-七里香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/029.范玮琪-最初的梦想.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/029.范玮琪-最初的梦想.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/028.苏永康-爱一个人好难.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/028.苏永康-爱一个人好难.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/027.萨顶顶-大名顶顶.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/027.萨顶顶-大名顶顶.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/026.那英-征服.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/026.那英-征服.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/025.陈星-流浪歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/025.陈星-流浪歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/024.梅艳芳-亲密爱人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/024.梅艳芳-亲密爱人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/023.李翊君-风中的承诺.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/023.李翊君-风中的承诺.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/022.任贤齐-流着泪的你的脸.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/022.任贤齐-流着泪的你的脸.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/021.王馨平-别问我是谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/021.王馨平-别问我是谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/020.汤宝如-缘分的天空.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/020.汤宝如-缘分的天空.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/019.李智楠-红色石头.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/019.李智楠-红色石头.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/018.黎瑞恩-雨季不再来.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/018.黎瑞恩-雨季不再来.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/017.张镐哲-好男人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/017.张镐哲-好男人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/016.周深-大鱼.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/016.周深-大鱼.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/015.腾格尔-天堂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/015.腾格尔-天堂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/014.汪峰-飞得更高.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/014.汪峰-飞得更高.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/013.潘美辰-我想有个家.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/013.潘美辰-我想有个家.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/012.张国荣-风继续吹.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/012.张国荣-风继续吹.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/011.满文军-懂你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/011.满文军-懂你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/010.张韶涵-阿刁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/010.张韶涵-阿刁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/009.巫启贤-太傻.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/009.巫启贤-太傻.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/008.那英-默.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/008.那英-默.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/007.赵雷-成都.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/007.赵雷-成都.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/006.陈琳-你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/006.陈琳-你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/005.周传雄-黄昏.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/005.周传雄-黄昏.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/002.李宗盛-山丘.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/002.李宗盛-山丘.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/001.光良-童话.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/001.光良-童话.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/004.游鸿明-下沙.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/004.游鸿明-下沙.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/专辑集/@eaDir/003.伍佰 And China Blue-挪威的森林.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "专辑集/@eaDir/003.伍佰 And China Blue-挪威的森林.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/第17集【合唱挑战】一人一句唱《富士山下》 - 小红书_video.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/第17集【合唱挑战】一人一句唱《富士山下》 - 小红书_video.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/爱上杀手 麦浚龙.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/爱上杀手 麦浚龙.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/江苏宜兴再发校园随机行凶事件，凶手的青春绝命书传递了什么？｜江苏宜兴｜无差别攻击｜徐加金｜无锡工艺职业技术学院｜恐怖袭击｜顶岗实习｜王局拍案20241118 - 王志安 (360p, h264).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/江苏宜兴再发校园随机行凶事件，凶手的青春绝命书传递了什么？｜江苏宜兴｜无差别攻击｜徐加金｜无锡工艺职业技术学院｜恐怖袭击｜顶岗实习｜王局拍案20241118 - 王志安 (360p, h264).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/庞 龙 ⧸ 两 只 蝴 蝶.flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/庞 龙 ⧸ 两 只 蝴 蝶.flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/喜新厌旧 - 张宇『喜新厌旧的你呀 能有多少热度的真心』【动态歌词Lyrics】.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/喜新厌旧 - 张宇『喜新厌旧的你呀 能有多少热度的真心』【动态歌词Lyrics】.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 黄昏 - 许绍洋.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 黄昏 - 许绍洋.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 高妹 - Hacken Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 高妹 - Hacken Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 飞花 - Hacken Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 飞花 - <PERSON><PERSON>n <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 飞得更高 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 飞得更高 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 飞女正传 - Miriam Yeung.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 飞女正传 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 飞云之下 - 韩红.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 飞云之下 - 韩红.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 风雨同路 - Paula Tsui.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 风雨同路 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 错错错 - 陈娟儿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 错错错 - 陈娟儿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 错爱的呼唤 - 郭富城.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 错爱的呼唤 - 郭富城.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 酒红色的心 - Alan Tam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 酒红色的心 - Alan <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 逼得宠物太紧 - Terence Siufay.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 逼得宠物太紧 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 记事本 - Kelly Chen.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 记事本 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 蝴蝶飞呀 - 小虎队.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 蝴蝶飞呀 - 小虎队.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 董小姐 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 董小姐 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 莉莉安 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 莉莉安 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 英雄故事 _電影 _警察故事_ 主題曲_ - Jackie Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 英雄故事 _電影 _警察故事_ 主題曲_ - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 苦口良药 - 陈慧珊.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 苦口良药 - 陈慧珊.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 花火 - Gigi Leung.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 花火 - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 花心 - Wakin Chau.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 花心 - <PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 胆小鬼 - Gigi Leung.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 胆小鬼 - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 缠绵游戏 - Edmond Leung.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 缠绵游戏 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 等 - Danny Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 等 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 空空 - 我是唱作人2第2期Live - 陈粒.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 空空 - 我是唱作人2第2期Live - 陈粒.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 看我72变 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 看我72变 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 皇后大道东 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 皇后大道东 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 痴心换情深 - Vivian Chow.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 痴心换情深 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 男兒當自強 - Jackie Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 男兒當自強 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 父亲写的散文诗 - Live - Li Jian.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 父亲写的散文诗 - Live - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱要怎么说出口 - Live - Jike Junyi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱要怎么说出口 - Live - Jike <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱的主打歌 - Elva Hsiao.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱的主打歌 - Elva Hsiao.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱江山更爱美人 _ 一剪梅 - 吴秀波.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱江山更爱美人 _ 一剪梅 - 吴秀波.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱是最大权利 - Kary Ng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱是最大权利 - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱情转移 - Eason Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱情转移 - <PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱情抗体 - 许慧欣.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱情抗体 - 许慧欣.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱情怎么了吗 - Crowd Lu.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱情怎么了吗 - Crowd Lu.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱得太迟 - Leo Ku.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱得太迟 - Leo Ku.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱如潮水 - Dick and Cowboy.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱如潮水 - <PERSON> and <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱天爱地 - Leon Lai (1).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱天爱地 - <PERSON> (1).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱与痛的边缘 - Live - Janice Vidal.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱与痛的边缘 - Live - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱不完 - Andy Lau.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱不完 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 爱X无限大 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 爱X无限大 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 焚情 - Christopher Wong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 焚情 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 烂泥 - Andy Hui.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 烂泥 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 激光中 - Roman Tam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 激光中 - Roman Tam.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 滚 - Edmond Leung.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 滚 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 海盗 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 海盗 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 浪花一朵朵 - Richie Jen.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 浪花一朵朵 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 橘子汽水 - Nan Quan Mama.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 橘子汽水 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 李白 - Ronghao Li.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - <PERSON> - <PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 曹操 - JJ Lin.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 曹操 - JJ <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 暧昧 - Rainie Yang.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 暧昧 - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 暧昧 - Faye Wong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 暧昧 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 暗里著迷 - 粤 - Andy Lau.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 暗里著迷 - 粤 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 暗涌 - 电影_愈快乐愈堕落_歌曲 - Anthony Wong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 暗涌 - 电影_愈快乐愈堕落_歌曲 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 春泥 - Harlem Yu.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 春泥 - Harlem Yu.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 春天里 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 春天里 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 春天花会开 - Richie Jen.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 春天花会开 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 春光乍泄 - Anthony Wong.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 春光乍泄 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 旧情绵绵 - 甄子丹.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 旧情绵绵 - 甄子丹.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 方寸大乱_合唱_ - 叶文辉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 方寸大乱_合唱_ - 叶文辉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 斑马_斑马 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 斑马_斑马 - 宋冬野.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 敢爱敢做 - George Lam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 敢爱敢做 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 抱一抱 - Jordan Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 抱一抱 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 忽然之间 - Karen Mok.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 忽然之间 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 当爱在靠近 - Rene Liu.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 当爱在靠近 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 当我想你的时候 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 当我想你的时候 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 当你 - Cyndi Wang.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 当你 - <PERSON><PERSON> Wang.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 广岛之恋 - Karen Mok.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 广岛之恋 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 布拉格广场 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 布拉格广场 - JOLIN.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 对面的女孩看过来 - Richie Jen.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 对面的女孩看过来 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 存在 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 存在 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 好心分手_合唱_ - 叶文辉.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 好心分手_合唱_ - 叶文辉.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 好人 - Justin Lo.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 好人 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 天命最高 - Louis Koo.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 天命最高 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 大风吹 - Live - 刘惜君.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 大风吹 - Live - 刘惜君.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 大浪漫主义 - Twins.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 大浪漫主义 - Twins.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 大地恩情 - 电视剧_大地恩情_主题曲 - Michael Kwan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 大地恩情 - 电视剧_大地恩情_主题曲 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 国境之南 - Fan Yi Chen.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 国境之南 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 哈利BOBO - BOBO.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 哈利BOBO - BOBO.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 后来 - Chilam.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 后来 - Chilam.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 合久必婚 - Hacken Lee.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 合久必婚 - Hacken Lee.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 可不可以 - Andy Lau.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 可不可以 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 半斤八两 - 电影_半斤八两_歌曲 - Sam Hui.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 半斤八两 - 电影_半斤八两_歌曲 - Sam <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 半情歌 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 半情歌 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 加德满都的风铃 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 加德满都的风铃 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 别怕我伤心 - Jeff Chang.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 别怕我伤心 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 冰雨 - Andy Lau.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 冰雨 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 冬季到台北来看雨 - 孟庭苇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 冬季到台北来看雨 - 孟庭苇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 光明 - Wang Feng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 光明 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 光年之外 _电影 _Passengers_ 中国区主题曲_ - G.E.M..mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 光年之外 _电影 _Passengers_ 中国区主题曲_ - G.E.M..mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 像我這種男人 - Louis Koo.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 像我這種男人 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 假如爱有天意 - Li Jian.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 假如爱有天意 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 倔强 - Mayday.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 倔强 - Mayday.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 传奇 - Li Jian.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 传奇 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 从不喜欢孤单一个 - 彭家丽.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 从不喜欢孤单一个 - 彭家丽.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 今期流行 - Louis Koo.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 今期流行 - <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 二十世纪少年 - Unplugged - Kary Ng.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 二十世纪少年 - Unplugged - <PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 九佰九拾九朵玫瑰 - 邰正宵.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 九佰九拾九朵玫瑰 - 邰正宵.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 东方之珠 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 东方之珠 - 罗大佑.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 不是我不小心 - 张镐哲.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 不是我不小心 - 张镐哲.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 不将就 _电影_何以笙箫默_片尾曲_ - Ronghao Li.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 不将就 _电影_何以笙箫默_片尾曲_ - Ronghao Li.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 99次我爱他 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 99次我爱他 - 元若蓝.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/SpotiMate.io - 1874 - Eason Chan.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/SpotiMate.io - 1874 - <PERSON><PERSON><PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/@eaDir/.DS_Store@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/.DS_Store@SynoResource"}, "/Volumes/music/虾米网易云/@eaDir/Thumbs.db@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/Thumbs.db@SynoResource"}, "/Volumes/music/虾米网易云/@eaDir/最近播放@SynoResource": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "@eaDir/最近播放@SynoResource"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/08-01.雨夜的浪漫-谭咏麟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/08-01.雨夜的浪漫-谭咏麟.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/去重后歌曲列表.txt": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/去重后歌曲列表.txt"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/歌曲列表.txt": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/歌曲列表.txt"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/黄色的月亮-苏慧伦 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/黄色的月亮-苏慧伦 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/黄昏-小刚 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/黄昏-小刚 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/驿动的心-姜育恒 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/驿动的心-姜育恒 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/风雨无阻.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/风雨无阻.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/风继续吹-张国荣 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/风继续吹-张国荣 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/风中的承诺-李翊君 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/风中的承诺-李翊君 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/领悟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/领悟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/顺流、逆流-徐晓凤 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/顺流、逆流-徐晓凤 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/难得有情人-关淑怡 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/难得有情人-关淑怡 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/随缘-温兆伦 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/随缘-温兆伦 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/酒干倘卖无.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/酒干倘卖无.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/都市夜归人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/都市夜归人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/那一场风花雪月的故事-周治平 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/那一场风花雪月的故事-周治平 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/遥远的她-张学友 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/遥远的她-张学友 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/过火.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/过火.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/让我欢喜让我忧-周华健 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/让我欢喜让我忧-周华健 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/让我一次爱个够-庾澄庆 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/让我一次爱个够-庾澄庆 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/让一切随风-钟镇涛 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/让一切随风-钟镇涛 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/认错-优客李林 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/认错-优客李林 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/蔓延.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/蔓延.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/蒙娜丽莎的眼泪-林志炫 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/蒙娜丽莎的眼泪-林志炫 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/至少还有你-林忆莲 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/至少还有你-林忆莲 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/老情歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/老情歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/约定-周惠.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/约定-周惠.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/第一次-光良 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/第一次-光良 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/童年.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/童年.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/移情别恋.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/移情别恋.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/秋来秋去-叶倩文 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/秋来秋去-叶倩文 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/短发-梁永琪 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/短发-梁永琪 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/真的爱你-Beyond.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/真的爱你-Beyond.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/真情人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/真情人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/白天不懂夜的黑.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/白天不懂夜的黑.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/痴心绝对-李圣杰 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/痴心绝对-李圣杰 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/痴心换情深-周慧敏 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/痴心换情深-周慧敏 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/痛苦的人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/痛苦的人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/痛你的责任-品冠 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/痛你的责任-品冠 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/用心良苦-张宇 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/用心良苦-张宇 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/生命之曲-林子祥 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/生命之曲-林子祥 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/玻璃心-扬林 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/玻璃心-扬林 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/现代爱情故事-张智霖.许秋怡 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/现代爱情故事-张智霖.许秋怡 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/独角戏-许茹芸 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/独角戏-许茹芸 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/特别的爱给特别的你-五思凯 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/特别的爱给特别的你-五思凯 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/牵手.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/牵手.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱那么重.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱那么重.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱要怎么说出口.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱要怎么说出口.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱的代价-张艾佳 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱的代价-张艾佳 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱我的人和我爱的人-裘海正 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱我的人和我爱的人-裘海正 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱我的人和我爱的人-游鸿明 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱我的人和我爱的人-游鸿明 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱我你怕了吗.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱我你怕了吗.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱情的故事.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱情的故事.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱在深秋-谭咏麟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱在深秋-谭咏麟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱你在心口难开.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱你在心口难开.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱你十分泪七分.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱你十分泪七分.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱你一万年.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱你一万年.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱不爱我.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱不爱我.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱上一个不回家的人-林忆莲 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱上一个不回家的人-林忆莲 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/爱一个人好难-苏永康 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/爱一个人好难-苏永康 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/漂洋过海来看你-娃娃 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/漂洋过海来看你-娃娃 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/海浪.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/海浪.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/海浪-黄品伟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/海浪-黄品伟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/浪子心声-许冠杰 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/浪子心声-许冠杰 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/浪人情歌-伍佰 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/浪人情歌-伍佰 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/河流-柯以敏 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/河流-柯以敏 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/没有情人的情人节.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/没有情人的情人节.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/永远到底有多远.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/永远到底有多远.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/水手.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/水手.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/水姻缘.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/水姻缘.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/橄榄树-齐豫 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/橄榄树-齐豫 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/棋子.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/棋子.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/梦醒时分-陈淑华 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/梦醒时分-陈淑华 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/枕着你的名字入睡.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/枕着你的名字入睡.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/来生缘.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/来生缘.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/望天-文章 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/望天-文章 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/朋友别哭.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/朋友别哭.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/朋友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/朋友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/有多少爱可以重来-黄仲坤 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/有多少爱可以重来-黄仲坤 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/月亮偷着哭-何静 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/月亮偷着哭-何静 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/月亮代表我的心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/月亮代表我的心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/最远的你是我最近的爱-车继铃 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/最远的你是我最近的爱-车继铃 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/最浪漫的事-赵咏华 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/最浪漫的事-赵咏华 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/曾经心疼.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/曾经心疼.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/暖昧-候湘婷 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/暖昧-候湘婷 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/晴天-周杰伦.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/晴天-周杰伦.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/是缘是债是场梦-刘锡明 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/是缘是债是场梦-刘锡明 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/是否完整的一无所有.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/是否完整的一无所有.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/星语心愿-张柏芝 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/星语心愿-张柏芝 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/无所谓-杨坤 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/无所谓-杨坤 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/无情的情书-动力火车 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/无情的情书-动力火车 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/无心伤害-杜德伟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/无心伤害-杜德伟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/新鸳鸯蝴蝶梦-黄安 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/新鸳鸯蝴蝶梦-黄安 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/新不了情-万芳 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/新不了情-万芳 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/故乡的云.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/故乡的云.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/摘下满天星-郑少秋 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/摘下满天星-郑少秋 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/挪威的森林.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/挪威的森林.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/把悲伤留给自己.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/把悲伤留给自己.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/执着.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/执着.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我这个你不爱的人-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我这个你不爱的人-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我要找到你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我要找到你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我要得不多-马兆骏 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我要得不多-马兆骏 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我知道你很难过-蔡依林 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我知道你很难过-蔡依林 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我的未来不是梦-张雨生 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我的未来不是梦-张雨生 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我的心太乱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我的心太乱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我用自己的方式爱你-陈明真 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我用自己的方式爱你-陈明真 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我爱你一天-吕芳 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我爱你一天-吕芳 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我是不是该安静的走开-郭富城 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我是不是该安静的走开-郭富城 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我是不是你最痛爱的人-潘越云 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我是不是你最痛爱的人-潘越云 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我愿意.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我愿意.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我想有个家-潘美晨 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我想有个家-潘美晨 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我很丑，可是我很温柔-赵传 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我很丑，可是我很温柔-赵传 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我和春天有个约会-刘雅丽 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我和春天有个约会-刘雅丽 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我可以抱你吗-张惠妹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我可以抱你吗-张惠妹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我不想说.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我不想说.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我不想再次被情伤.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我不想再次被情伤.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/想你的时候-千百惠 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/想你的时候-千百惠 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/情非得已-庚澄庆 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/情非得已-庚澄庆 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/情人-杜德伟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/情人-杜德伟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/恋曲1990.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/恋曲1990.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/恋人未满-SHE .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/恋人未满-SHE .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/忘记你我做不到.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/忘记你我做不到.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/忘情水.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/忘情水.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/心要让你听见.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/心要让你听见.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/很爱很爱你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/很爱很爱你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/征服-那英 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/征服-那英 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/往事随风.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/往事随风.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/当爱已成往事.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/当爱已成往事.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/寂寞难耐-李宗盛 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/寂寞难耐-李宗盛 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/如果这都不算爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/如果这都不算爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/女人花.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/女人花.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/失恋-草蜢 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/失恋-草蜢 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/太委屈-陶晶莹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/太委屈-陶晶莹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/太傻-巫咎贤 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/太傻-巫咎贤 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/天黑黑-孙燕姿 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/天黑黑-孙燕姿 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/天黑-阿杜 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/天黑-阿杜 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/大约在冬季-齐秦 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/大约在冬季-齐秦 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/大海.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/大海.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/大哥.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/大哥.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/夜曲-周杰伦.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/夜曲-周杰伦.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/堆积情感-邝美云 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/堆积情感-邝美云 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/城里的月光-许美静 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/城里的月光-许美静 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/在我生命里-罗文 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/在我生命里-罗文 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/在回首.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/在回首.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/回家-顺子 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/回家-顺子 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/囚鸟-彭羚 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/囚鸟-彭羚 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/唯一-王力宏 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/唯一-王力宏 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/哭砂-黄莺莺 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/哭砂-黄莺莺 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/味道-辛晓琪 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/味道-辛晓琪 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/吻别.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/吻别.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/只要你过得比我好-钟镇涛 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/只要你过得比我好-钟镇涛 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/口是心非.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/口是心非.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/单身情歌-林志炫 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/单身情歌-林志炫 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/千载不变-温拿 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/千载不变-温拿 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/千年等一回.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/千年等一回.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/千千阙歌-陈慧娴 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/千千阙歌-陈慧娴 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/北门星-温岚 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/北门星-温岚 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/勇气-梁静茹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/勇气-梁静茹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/剪爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/剪爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/别问我是谁-王磬平 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/别问我是谁-王磬平 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/别让我一个人醉.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/别让我一个人醉.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/别怕我伤心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/别怕我伤心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/分飞.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/分飞.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/出界.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/出界.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/冷酷到底-羽泉 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/冷酷到底-羽泉 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/再见也是朋友-何婉盈.曾航生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/再见也是朋友-何婉盈.曾航生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/再回到从前.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/再回到从前.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/其实你不懂我的心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/其实你不懂我的心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/全人类高歌-太極 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/全人类高歌-太極 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/光阴的故事.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/光阴的故事.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/像雾像雨又像风.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/像雾像雨又像风.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/偏偏喜欢你-陈百强 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/偏偏喜欢你-陈百强 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/值得一辈子去爱-纪如景 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/值得一辈子去爱-纪如景 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/值得-郑秀文 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/值得-郑秀文 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你要的爱-戴佩妮 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你要的爱-戴佩妮 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你看你看月亮的脸-孟庭肇 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你看你看月亮的脸-孟庭肇 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你的眼神.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你的眼神.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你的眼睛背叛你的心-郑中基 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你的眼睛背叛你的心-郑中基 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你的样子.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你的样子.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你爱我像谁-张卫健 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你爱我像谁-张卫健 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你是我最深爱的人-永邦 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你是我最深爱的人-永邦 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你把我的女人带走.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你把我的女人带走.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/你把我灌醉-黄大炜 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/你把我灌醉-黄大炜 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/伤痕-林忆莲 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/伤痕-林忆莲 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/伤心太平洋.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/伤心太平洋.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/以来你什么都不要-张惠妹 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/以来你什么都不要-张惠妹 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/他不爱我-莫文蔚 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/他不爱我-莫文蔚 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/今夜你会不会来-黎明 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/今夜你会不会来-黎明 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/亲密爱人-梅艳芳 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/亲密爱人-梅艳芳 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/为爱痴狂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/为爱痴狂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/为爱疯狂-刘若英 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/为爱疯狂-刘若英 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/为爱犯了罪-李度 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/为爱犯了罪-李度 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/为情所困-梁朝伟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/为情所困-梁朝伟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/为什么背着我爱别人-许志安 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/为什么背着我爱别人-许志安 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/不让我的眼泪陪我过夜.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/不让我的眼泪陪我过夜.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/不能停的爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/不能停的爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/不管有多苦.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/不管有多苦.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/不是我不小心-张镐哲 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/不是我不小心-张镐哲 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/下沙-游鸿明 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/下沙-游鸿明 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/三万英尺-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/三万英尺-迪克牛仔 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/万水千山总是情-汪明荃 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/万水千山总是情-汪明荃 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一笑而过-那英 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一笑而过-那英 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一生爱你千百回.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一生爱你千百回.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一样的月亮-苏芮 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一样的月亮-苏芮 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一天一点爱恋-梁朝伟 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一天一点爱恋-梁朝伟 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一千零一个愿望-4inlove .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一千零一个愿望-4inlove .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一人有一个梦想-黎瑞恩 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一人有一个梦想-黎瑞恩 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/999朵玫瑰-邰正宵 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/999朵玫瑰-邰正宵 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/34-08.那些年-胡夏.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/34-08.那些年-胡夏.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/33-09.以身试爱-关心妍.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/33-09.以身试爱-关心妍.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/33-05.天梯-CAllstar.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/33-05.天梯-CAllstar.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/32-04.我的回忆不是我的-泳儿、海鸣威.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/32-04.我的回忆不是我的-泳儿、海鸣威.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/32-03.你瞒我瞒-陈柏宇.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/32-03.你瞒我瞒-陈柏宇.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/31-08.爱不疚-林峰.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/31-08.爱不疚-林峰.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/31-03.一事无成-郑融、周柏豪.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/31-03.一事无成-郑融、周柏豪.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/31-02.喜贴街-谢安琪.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/31-02.喜贴街-谢安琪.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/30-09.电灯胆-邓丽欣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/30-09.电灯胆-邓丽欣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/30-07.逼得太紧-吴雨霏.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/30-07.逼得太紧-吴雨霏.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/30-06.酷爱-张敬轩.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/30-06.酷爱-张敬轩.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/29-04.情歌-侧田.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/29-04.情歌-侧田.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/28-04.老鼠爱大米-王启文.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/28-04.老鼠爱大米-王启文.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/27-08.奇洛里维斯回信-薛凯琪.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/27-08.奇洛里维斯回信-薛凯琪.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/27-07.好好恋爱-方力申.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/27-07.好好恋爱-方力申.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/27-06.小城大事-杨千嬅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/27-06.小城大事-杨千嬅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/27-03.饮歌-Twins.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/27-03.饮歌-Twins.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/27-02.爱与诚-古巨基.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/27-02.爱与诚-古巨基.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-10.左邻右里-谭咏麟、李克勤.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-10.左邻右里-谭咏麟、李克勤.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-08.好心好报-方力申.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-08.好心好报-方力申.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-07.无间道-刘德华、梁朝伟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-07.无间道-刘德华、梁朝伟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-06.我的骄傲-容祖儿.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-06.我的骄傲-容祖儿.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-05.十面埋伏-陈奕迅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-05.十面埋伏-陈奕迅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-02.三角志-卢巧音.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-02.三角志-卢巧音.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/26-01.可惜我是水瓶座-杨千嬅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/26-01.可惜我是水瓶座-杨千嬅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/25-06.争气-容祖儿.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/25-06.争气-容祖儿.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/25-05.明年今日-陈奕迅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/25-05.明年今日-陈奕迅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/25-04.好心分手-卢巧音.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/25-04.好心分手-卢巧音.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/25-03.有福气-陈慧琳.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/25-03.有福气-陈慧琳.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/24-09.Para Para Sakura-郭富城.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/24-09.Para Para Sakura-郭富城.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/24-08.痛爱-容祖儿.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/24-08.痛爱-容祖儿.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/24-05.夏日Fiesta-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/24-05.夏日Fiesta-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/24-02.终身美丽-郑秀文.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/24-02.终身美丽-郑秀文.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/24-01.烂泥-许志安.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/24-01.烂泥-许志安.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/23-09.少女的祈祷-杨千嬅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/23-09.少女的祈祷-杨千嬅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/23-03.给自己的情书-王菲.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/23-03.给自己的情书-王菲.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/23-02.感情线上-郑秀文.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/23-02.感情线上-郑秀文.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/23-01.K歌之王-陈奕迅.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/23-01.K歌之王-陈奕迅.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-12.左右手-张国荣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-12.左右手-张国荣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-11.眼睛想旅行-黎明.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-11.眼睛想旅行-黎明.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-10.教我如何不爱他-许志安、叶德娴.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-10.教我如何不爱他-许志安、叶德娴.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-09.插曲-郑秀文.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-09.插曲-郑秀文.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-06.非走不可-谢霆锋.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-06.非走不可-谢霆锋.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-03.真心真意-许志安.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-03.真心真意-许志安.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/21-06.越吻越伤心-苏永康.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/21-06.越吻越伤心-苏永康.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/20-10.爱是永恒-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/20-10.爱是永恒-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/20-04.爱的呼唤-郭富城.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/20-04.爱的呼唤-郭富城.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/20-03.明知故犯-许美静.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/20-03.明知故犯-许美静.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/20-02.中国人-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/20-02.中国人-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/20-01.不老的传说-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/20-01.不老的传说-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/19-09.你的名字，我的姓氏-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/19-09.你的名字，我的姓氏-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/19-08.最激帝国-郭富城.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/19-08.最激帝国-郭富城.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/19-04.友情岁月-郑伊健.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/19-04.友情岁月-郑伊健.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/19-01.情深说话未曾讲-黎明.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/19-01.情深说话未曾讲-黎明.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/18-03.离开以后-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/18-03.离开以后-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/17-06.饿狼传说-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/17-06.饿狼传说-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/17-05.哪有一天不想你-黎明.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/17-05.哪有一天不想你-黎明.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/17-04.谁人知-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/17-04.谁人知-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/16-09.等你回来-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/16-09.等你回来-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/16-05.谢谢你的爱-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/16-05.谢谢你的爱-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/16-04.狂野之城-郭富城.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/16-04.狂野之城-郭富城.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/16-02.夏日倾情-黎明.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/16-02.夏日倾情-黎明.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/15-07.但愿不只是朋友-黎明.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/15-07.但愿不只是朋友-黎明.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/15-01.容易受伤的女人-王靖雯.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/15-01.容易受伤的女人-王靖雯.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/14-01.每天爱你多一些-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/14-01.每天爱你多一些-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/13-08.特别的爱给特别的你-伍思凯.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/13-08.特别的爱给特别的你-伍思凯.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/13-07.夕阳醉了-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/13-07.夕阳醉了-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/13-04.你知道我在等你吗-张洪量.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/13-04.你知道我在等你吗-张洪量.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/13-03.相逢何必曾相识-蒋志光、韦绮姗.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/13-03.相逢何必曾相识-蒋志光、韦绮姗.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/12-03.千千阕歌-陈慧娴.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/12-03.千千阕歌-陈慧娴.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/12-02.谁明浪子心-王杰.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/12-02.谁明浪子心-王杰.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/11-09.大约在冬季-齐秦.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/11-09.大约在冬季-齐秦.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/11-08.沉默是金-张国荣、许冠杰.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/11-08.沉默是金-张国荣、许冠杰.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/100.孙露-鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/100.孙露-鬼迷心窍.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/10-08.倾心-Raidas.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/10-08.倾心-Raidas.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/10-07.无心睡眠-张国荣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/10-07.无心睡眠-张国荣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/10-01.太阳星辰-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/10-01.太阳星辰-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/099.郭峰-永远.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/099.郭峰-永远.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/098.吴奇隆-祝你一路顺风.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/098.吴奇隆-祝你一路顺风.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/097.刘嘉亮-你到底爱谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/097.刘嘉亮-你到底爱谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/096.林志颖-十七岁的雨季.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/096.林志颖-十七岁的雨季.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/095.陶喆-就是爱你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/095.陶喆-就是爱你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/094.黄凯芹-雨中的恋人们.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/094.黄凯芹-雨中的恋人们.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/092.张宇-月亮惹的祸.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/092.张宇-月亮惹的祸.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/091.薛之谦-演员.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/091.薛之谦-演员.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/090.李晓杰-朋友的酒.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/090.李晓杰-朋友的酒.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/09-10.当年情-张国荣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/09-10.当年情-张国荣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/089.李圣杰-痴心绝对.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/089.李圣杰-痴心绝对.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/088.裘海正-爱我的人和我爱的人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/088.裘海正-爱我的人和我爱的人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/087.容祖儿-小小.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/087.容祖儿-小小.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/085.许韶洋-花香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/085.许韶洋-花香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/084.周慧敏-最爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/084.周慧敏-最爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/083.林依轮-爱情鸟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/083.林依轮-爱情鸟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/082.五月天-突然好想你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/082.五月天-突然好想你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/080.无印良品-掌心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/080.无印良品-掌心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-10.爱情陷阱-谭咏麟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-10.爱情陷阱-谭咏麟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-09.顺流·逆流-徐小凤.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-09.顺流·逆流-徐小凤.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-07.情已逝-张学友.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-07.情已逝-张学友.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-06.蔓珠沙华-梅艳芳.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-06.蔓珠沙华-梅艳芳.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-03.不羁的风-张国荣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-03.不羁的风-张国荣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/08-01.雨夜的浪漫-谭咏麟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/08-01.雨夜的浪漫-谭咏麟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/079.动力火车-终于明白.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/079.动力火车-终于明白.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/078.旭日阳刚-怀念青春.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/078.旭日阳刚-怀念青春.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/077.信乐团-死了都要爱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/077.信乐团-死了都要爱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/075.邰正宵-想你想得好孤寂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/075.邰正宵-想你想得好孤寂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/074.高胜美-情难枕.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/074.高胜美-情难枕.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/072.邝美云-我和春天有个约会.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/072.邝美云-我和春天有个约会.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/070.庾澄庆-情非得已.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/070.庾澄庆-情非得已.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/07-01.Monica-张国荣.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/07-01.Monica-张国荣.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/069.毛宁-涛声依旧.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/069.毛宁-涛声依旧.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/068.羽·泉-彩虹.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/068.羽·泉-彩虹.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/067.谢安琪-钟无艳.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/067.谢安琪-钟无艳.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/066.张学友-一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/066.张学友-一千个伤心的理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/065.永邦-你是我最深爱的人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/065.永邦-你是我最深爱的人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/064.沙宝亮-暗香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/064.沙宝亮-暗香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/063.小沈阳_高进-我的好兄弟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/063.小沈阳_高进-我的好兄弟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/062.张雨生-大海.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/062.张雨生-大海.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/061.杨宗纬-洋葱.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/061.杨宗纬-洋葱.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/060.王杰-为了爱梦一生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/060.王杰-为了爱梦一生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/06-10.世间始终你好-罗文、甄妮.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/06-10.世间始终你好-罗文、甄妮.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/06-06.你的眼神-蔡琴.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/06-06.你的眼神-蔡琴.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/059.张卫健-你爱我像谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/059.张卫健-你爱我像谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/058.林忆莲_李宗盛-当爱已成往事.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/058.林忆莲_李宗盛-当爱已成往事.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/057.李行亮-愿得一人心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/057.李行亮-愿得一人心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/056.侯湘婷-暧昧.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/056.侯湘婷-暧昧.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/055.林宥嘉-你是我的眼.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/055.林宥嘉-你是我的眼.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/054.黄龄-High歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/054.黄龄-High歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/053.张学友-慢慢.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/053.张学友-慢慢.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/052.孙楠-来世还要在一起.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/052.孙楠-来世还要在一起.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/051.张惠妹-我最亲爱的.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/051.张惠妹-我最亲爱的.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/050.关喆-想你的夜.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/050.关喆-想你的夜.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/049.张震岳-爱我别走.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/049.张震岳-爱我别走.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/048.赵传-我终于失去了你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/048.赵传-我终于失去了你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/047.田震-执着.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/047.田震-执着.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/046.柯以敏-河流.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/046.柯以敏-河流.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/045.彭羚-囚鸟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/045.彭羚-囚鸟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/044.陈晓东-我比谁都清楚.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/044.陈晓东-我比谁都清楚.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/043.辛晓琪-领悟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/043.辛晓琪-领悟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/042.成龙_金喜善-美丽的神话.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/042.成龙_金喜善-美丽的神话.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/出嫁-张清芳 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/出嫁-张清芳 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一辈子的孤单.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一辈子的孤单.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/041.何润东-没有我你怎么办.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/041.何润东-没有我你怎么办.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/040.杨林-玻璃心.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/040.杨林-玻璃心.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/04-08.东方之珠-甄妮.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/04-08.东方之珠-甄妮.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/04-07.旧梦不须记-雷安娜.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/04-07.旧梦不须记-雷安娜.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/039.ALL-RANGE-樱花草.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/039.ALL-RANGE-樱花草.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/036.徐怀钰-我是女生.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/036.徐怀钰-我是女生.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/034.汤潮-狼爱上羊.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/034.汤潮-狼爱上羊.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/033.吕方-老情歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/033.吕方-老情歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/032.迪克牛仔-有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/032.迪克牛仔-有多少爱可以重来.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/030.周杰伦-七里香.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/030.周杰伦-七里香.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/03-06.京华春梦-汪明荃.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/03-06.京华春梦-汪明荃.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/03-02.上海滩-叶丽仪.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/03-02.上海滩-叶丽仪.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/03-01.人在旅途洒泪时-关正杰、雷安娜.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/03-01.人在旅途洒泪时-关正杰、雷安娜.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/029.范玮琪-最初的梦想.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/029.范玮琪-最初的梦想.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/027.萨顶顶-大名顶顶.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/027.萨顶顶-大名顶顶.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/025.陈星-流浪歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/025.陈星-流浪歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/024.梅艳芳-亲密爱人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/024.梅艳芳-亲密爱人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/023.李翊君-风中的承诺.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/023.李翊君-风中的承诺.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/022.任贤齐-流着泪的你的脸.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/022.任贤齐-流着泪的你的脸.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/021.王馨平-别问我是谁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/021.王馨平-别问我是谁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/020.汤宝如-缘分的天空.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/020.汤宝如-缘分的天空.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/02-07.眼泪为你流-陈百强.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/02-07.眼泪为你流-陈百强.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/02-05.陌上归人-区瑞强.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/02-05.陌上归人-区瑞强.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/02-01.天蚕变-关正杰.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/02-01.天蚕变-关正杰.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/019.李智楠-红色石头.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/019.李智楠-红色石头.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/018.黎瑞恩-雨季不再来.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/018.黎瑞恩-雨季不再来.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/017.张镐哲-好男人.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/017.张镐哲-好男人.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/016.周深-大鱼.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/016.周深-大鱼.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/015.腾格尔-天堂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/015.腾格尔-天堂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/014.汪峰-飞得更高.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/014.汪峰-飞得更高.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/012.张国荣-风继续吹.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/012.张国荣-风继续吹.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/011.满文军-懂你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/011.满文军-懂你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/010.张韶涵-阿刁.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/010.张韶涵-阿刁.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/01-09.愿君心记取-张德兰.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/01-09.愿君心记取-张德兰.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/01-07.誓要入刀山-郑少秋.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/01-07.誓要入刀山-郑少秋.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/01-06.倚天屠龙记-郑少秋.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/01-06.倚天屠龙记-郑少秋.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/01-01.小李飞刀-罗文.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/01-01.小李飞刀-罗文.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/008.那英-默.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/008.那英-默.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/007.赵雷-成都.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/007.赵雷-成都.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/006.陈琳-你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/006.陈琳-你的柔情我永远不懂.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/005.周传雄-黄昏.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/005.周传雄-黄昏.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/004.游鸿明-下沙.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/004.游鸿明-下沙.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/003.伍佰 And China Blue-挪威的森林.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/003.伍佰 And China Blue-挪威的森林.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/002.李宗盛-山丘.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/002.李宗盛-山丘.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/陶喆 - 就是爱你.flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/陶喆 - 就是爱你.flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/遇见-孙燕姿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/遇见-孙燕姿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/追光者-岑宁儿.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/追光者-岑宁儿.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/起风了-吴青峰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/起风了-吴青峰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/赵露-我是一只小小鸟.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/赵露-我是一只小小鸟.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/走在冷风中-刘思涵.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/走在冷风中-刘思涵.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/说散就散-JC 陈咏桐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/说散就散-JC 陈咏桐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/讲不出再见-谭咏麟.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/讲不出再见-谭咏麟.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/绿色-陈雪凝.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/绿色-陈雪凝.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/痴心绝对-李圣杰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/痴心绝对-李圣杰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/理想三旬-陈鸿宇.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/理想三旬-陈鸿宇.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/潘玮柏、弦子 - 不得不爱.flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/潘玮柏、弦子 - 不得不爱.flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/流星雨-F4.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/流星雨-F4.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/汪明荃-万水千山总是情-(无线电视剧《万水千总是情》主题曲).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/汪明荃-万水千山总是情-(无线电视剧《万水千总是情》主题曲).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/李白-李荣浩.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/李白-李荣浩.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/曹操-林俊杰.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/曹操-林俊杰.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/无心伤害_杜德伟.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/无心伤害_杜德伟.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/我是一只小小鸟_赵传.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/我是一只小小鸟_赵传.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/我是一只小小鸟_任贤齐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/我是一只小小鸟_任贤齐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/我很快乐-刘惜君.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/我很快乐-刘惜君.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/张碧晨 - 我只在乎你(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/张碧晨 - 我只在乎你(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/像我这样的人-毛不易.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/像我这样的人-毛不易.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/你怎么舍得我难过_黄品源.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/你怎么舍得我难过_黄品源.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/余枫 - 至少还有你(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/余枫 - 至少还有你(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/余枫 - 牧马人(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/余枫 - 牧马人(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/余枫 - 无所谓(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/余枫 - 无所谓(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/余枫 - Treasure(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/余枫 - Treasure(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/余枫 & 杨坤 - 空城(Live).mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/余枫 & 杨坤 - 空城(Live).mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/丑八怪-薛之谦.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/丑八怪-薛之谦.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/一万个理由-郑源.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/一万个理由-郑源.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/SpotiMate.io - 爱要怎么说出口 - Live - Jike Junyi.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/SpotiMate.io - 爱要怎么说出口 - Live - Jike <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/SpotiMate.io - 曹操 - JJ Lin.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/SpotiMate.io - 曹操 - JJ <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/Hope - 你不是真正的快乐.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/Hope - 你不是真正的快乐.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/2018中国好声音 - 说散就散 (1).flac/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/2018中国好声音 - 说散就散 (1).flac/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/12 丁文淞 - 第一次.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/12 丁文淞 - 第一次.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/02 李维，周深 - 贝加尔湖畔.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/02 李维，周深 - 贝加尔湖畔.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/002.李宗盛-山丘.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/002.李宗盛-山丘.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/005.周传雄-黄昏.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/005.周传雄-黄昏.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/036.徐怀钰-我是女生.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/036.徐怀钰-我是女生.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/不是我不小心-张镐哲 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/不是我不小心-张镐哲 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/冷酷到底-羽泉 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/冷酷到底-羽泉 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/太委屈-陶晶莹 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/太委屈-陶晶莹 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/征服-那英 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/征服-那英 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/我想有个家-潘美晨 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/我想有个家-潘美晨 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/我想有个家.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/我想有个家.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/我是一只小小鸟.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/我是一只小小鸟.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/我是不是你最痛爱的人-潘越云 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/我是不是你最痛爱的人-潘越云 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/我的心太乱.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/我的心太乱.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/无心伤害-杜德伟 .lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/无心伤害-杜德伟 .lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/爱要怎么说出口.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/爱要怎么说出口.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/真的爱你-Beyond.lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/真的爱你-Beyond.lrc"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/我是一只小小鸟.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/我是一只小小鸟.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/开不了口-周杰伦 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/开不了口-周杰伦 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/寂寞在唱歌.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/寂寞在唱歌.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/不装饰你的梦-蔡国权 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/不装饰你的梦-蔡国权 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一起走过的日子-刘德华 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一起走过的日子-刘德华 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一言难尽.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一言难尽.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/一生不变-李克勤 .wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/一生不变-李克勤 .wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/23-08.男人哭吧不是罪-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/23-08.男人哭吧不是罪-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/22-04.木鱼与金鱼-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/22-04.木鱼与金鱼-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/21-09.你是我的女人-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/21-09.你是我的女人-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/18-05.真永远-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/18-05.真永远-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/17-01.忘情水-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/17-01.忘情水-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/16-06.独自去偷欢-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/16-06.独自去偷欢-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/15-06.真我的风采-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/15-06.真我的风采-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/14-03.一起走过的日子-刘德华.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/14-03.一起走过的日子-刘德华.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/13-06.失恋-草蜢.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/13-06.失恋-草蜢.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/12-01.一生不变-李克勤.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/12-01.一生不变-李克勤.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/11-03.傻女-陈慧娴.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/11-03.傻女-陈慧娴.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/081.郑源-一万个理由.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/081.郑源-一万个理由.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/073.张信哲-爱如潮水.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/073.张信哲-爱如潮水.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/038.谭咏麟-讲不出再见.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/038.谭咏麟-讲不出再见.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/037.李克勤-月半小夜曲.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/037.李克勤-月半小夜曲.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/WMA 微软格式欠缺替换/@eaDir/031.伍思凯-特别的爱给特别的你.wma/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "WMA 微软格式欠缺替换/@eaDir/031.伍思凯-特别的爱给特别的你.wma/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/张碧晨 - 红玫瑰 (Live).lrc": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/张碧晨 - 红玫瑰 (Live).lrc"}, "/Volumes/music/虾米网易云/流行/@eaDir/张碧晨 - 红玫瑰 (Live).ogg/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/张碧晨 - 红玫瑰 (Live).ogg/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/云烟成雨-房东的猫.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/云烟成雨-房东的猫.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/SpotiMate.io - 梦醒时分 - Dick and Cowboy.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/SpotiMate.io - 梦醒时分 - <PERSON> and Cowboy.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/SpotiMate.io - 有多少爱可以重来 - Dick and Cowboy.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/SpotiMate.io - 有多少爱可以重来 - <PERSON> and Cowboy.mp3/SYNOINDEX_MEDIA_INFO"}, "/Volumes/music/虾米网易云/流行/@eaDir/SpotiMate.io - 为什么你背著我爱别人 - Andy Hui.mp3/SYNOINDEX_MEDIA_INFO": {"rootPath": "/Volumes/music/虾米网易云", "relPath": "流行/@eaDir/SpotiMate.io - 为什么你背著我爱别人 - Andy <PERSON>.mp3/SYNOINDEX_MEDIA_INFO"}, "/Users/<USER>/Documents/augment-projects/HEIC_Converter_Setup.md": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "HEIC_Converter_Setup.md"}, "/Users/<USER>/Documents/augment-projects/KM_PicGo_Setup.md": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "KM_PicGo_Setup.md"}, "/Users/<USER>/Documents/augment-projects/heic_converter_uploader.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "heic_converter_uploader.py"}, "/Users/<USER>/Documents/augment-projects/heic_to_png_converter.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "heic_to_png_converter.py"}, "/Users/<USER>/Documents/augment-projects/rename/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "rename/README.md"}, "/Users/<USER>/Documents/augment-projects/rename/music_stats.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "rename/music_stats.py"}, "/Users/<USER>/Documents/augment-projects/rename/music_stats.txt": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "rename/music_stats.txt"}, "/Users/<USER>/Documents/augment-projects/check/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/README.md"}, "/Users/<USER>/Documents/augment-projects/check/debug_album_scanner.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/debug_album_scanner.py"}, "/Users/<USER>/Documents/augment-projects/check/install_requirements.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/install_requirements.sh"}, "/Users/<USER>/Documents/augment-projects/check/interactive_music_organizer.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/interactive_music_organizer.py"}, "/Users/<USER>/Documents/augment-projects/check/music_organizer.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/music_organizer.py"}, "/Users/<USER>/Documents/augment-projects/check/quick_setup.py": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/quick_setup.py"}, "/Users/<USER>/Documents/augment-projects/check/run_music_organizer.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/run_music_organizer.sh"}, "/Users/<USER>/Documents/augment-projects/check/music_organizer.log": {"rootPath": "/Users/<USER>/Documents/augment-projects", "relPath": "check/music_organizer.log"}}