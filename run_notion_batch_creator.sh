#!/bin/bash
# Keyboard Maestro触发脚本 - Notion批量创建工具
# 从剪贴板读取标题列表并批量创建Notion条目

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查requests库是否安装
if ! python3 -c "import requests" &> /dev/null; then
    echo "📦 正在安装requests库..."
    pip3 install requests
fi

# 运行Python脚本
echo "🚀 启动Notion批量创建工具..."
python3 notion_batch_creator.py

# 暂停以便查看结果
echo ""
echo "按任意键继续..."
read -n 1 -s
