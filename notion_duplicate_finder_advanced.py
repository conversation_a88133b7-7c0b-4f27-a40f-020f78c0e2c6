#!/usr/bin/env python3
"""
Notion数据库重复标题检测工具 - 高级版本
包含更多功能：排序、统计、删除建议等
"""

import requests
import json
from collections import defaultdict
import sys
import os
from datetime import datetime

# 导入配置
try:
    from config import NOTION_API_TOKEN, DATABASE_ID, NOTION_VERSION
except ImportError:
    NOTION_API_TOKEN = "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"
    DATABASE_ID = "23895d7611248123a7fcf8b25d2d7998"
    NOTION_VERSION = "2022-06-28"

# API请求头
HEADERS = {
    "Authorization": f"Bearer {NOTION_API_TOKEN}",
    "Content-Type": "application/json",
    "Notion-Version": NOTION_VERSION
}

def get_all_database_entries():
    """获取数据库中的所有条目"""
    url = f"https://api.notion.com/v1/databases/{DATABASE_ID}/query"
    
    all_entries = []
    has_more = True
    start_cursor = None
    
    while has_more:
        payload = {"page_size": 100}
        if start_cursor:
            payload["start_cursor"] = start_cursor
        
        try:
            response = requests.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            
            data = response.json()
            all_entries.extend(data.get("results", []))
            
            has_more = data.get("has_more", False)
            start_cursor = data.get("next_cursor")
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return None
    
    return all_entries

def extract_title_from_page(page):
    """从页面对象中提取标题"""
    try:
        properties = page.get("properties", {})
        
        # 查找标题属性
        for prop_name, prop_value in properties.items():
            if prop_value.get("type") == "title":
                title_array = prop_value.get("title", [])
                if title_array:
                    return "".join([text.get("plain_text", "") for text in title_array])
        
        # 备用方案
        if "properties" in page and "Name" in page["properties"]:
            name_prop = page["properties"]["Name"]
            if name_prop.get("type") == "title":
                title_array = name_prop.get("title", [])
                if title_array:
                    return "".join([text.get("plain_text", "") for text in title_array])
        
        return "无标题"
        
    except Exception as e:
        print(f"⚠️  提取标题时出错: {e}")
        return "提取失败"

def parse_datetime(datetime_str):
    """解析ISO格式的日期时间字符串"""
    try:
        return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
    except:
        return None

def find_duplicates_advanced(entries):
    """查找重复的标题并提供详细分析"""
    title_to_pages = defaultdict(list)
    
    for entry in entries:
        title = extract_title_from_page(entry)
        if title and title.strip():
            created_time = parse_datetime(entry.get("created_time", ""))
            last_edited_time = parse_datetime(entry.get("last_edited_time", ""))
            
            title_to_pages[title.strip()].append({
                "id": entry.get("id"),
                "url": entry.get("url"),
                "created_time": entry.get("created_time"),
                "last_edited_time": entry.get("last_edited_time"),
                "created_datetime": created_time,
                "last_edited_datetime": last_edited_time
            })
    
    # 找出重复的标题并排序
    duplicates = {}
    for title, pages in title_to_pages.items():
        if len(pages) > 1:
            # 按创建时间排序
            pages.sort(key=lambda x: x['created_datetime'] or datetime.min)
            duplicates[title] = pages
    
    return duplicates

def format_advanced_output(duplicates, total_entries):
    """格式化高级输出结果"""
    if not duplicates:
        return f"✅ 在 {total_entries} 个条目中没有发现重复的标题！"
    
    output = []
    total_duplicates = sum(len(pages) for pages in duplicates.values())
    duplicate_count = len(duplicates)
    
    output.append(f"📊 数据库统计:")
    output.append(f"   • 总条目数: {total_entries}")
    output.append(f"   • 重复标题数: {duplicate_count}")
    output.append(f"   • 重复条目数: {total_duplicates}")
    output.append(f"   • 可清理条目数: {total_duplicates - duplicate_count}")
    output.append("")
    
    output.append(f"🔍 发现 {duplicate_count} 个重复的标题：\n")
    
    for i, (title, pages) in enumerate(duplicates.items(), 1):
        output.append(f"📝 {i}. 标题: '{title}' (重复 {len(pages)} 次)")
        
        # 标记最早的条目（建议保留）
        oldest_page = pages[0]  # 已按创建时间排序
        
        for j, page in enumerate(pages, 1):
            is_oldest = page['id'] == oldest_page['id']
            status = "🟢 建议保留" if is_oldest else "🔴 建议删除"
            
            output.append(f"   {j}. {status}")
            output.append(f"      ID: {page['id']}")
            output.append(f"      URL: {page['url']}")
            output.append(f"      创建时间: {page['created_time']}")
            output.append(f"      最后编辑: {page['last_edited_time']}")
        output.append("")
    
    # 添加操作建议
    output.append("💡 操作建议:")
    output.append("   1. 点击URL查看每个重复条目的内容")
    output.append("   2. 比较内容，确定要保留的版本")
    output.append("   3. 手动删除不需要的重复条目")
    output.append("   4. 建议保留创建时间最早的条目（标记为🟢）")
    output.append("")
    
    return "\n".join(output)

def generate_deletion_script(duplicates):
    """生成删除脚本（仅供参考，不自动执行）"""
    script_lines = []
    script_lines.append("# Notion重复条目删除脚本")
    script_lines.append("# 警告：请仔细检查后再执行！")
    script_lines.append("")
    
    for title, pages in duplicates.items():
        script_lines.append(f"# 标题: {title}")
        oldest_page = pages[0]
        
        for page in pages[1:]:  # 跳过最早的条目
            script_lines.append(f"# 删除: {page['url']}")
            script_lines.append(f"# curl -X PATCH 'https://api.notion.com/v1/pages/{page['id']}' \\")
            script_lines.append(f"#   -H 'Authorization: Bearer {NOTION_API_TOKEN}' \\")
            script_lines.append(f"#   -H 'Content-Type: application/json' \\")
            script_lines.append(f"#   -H 'Notion-Version: {NOTION_VERSION}' \\")
            script_lines.append(f"#   -d '{{\"archived\": true}}'")
            script_lines.append("")
    
    return "\n".join(script_lines)

def main():
    """主函数"""
    print("🚀 开始搜索Notion数据库...")
    
    # 获取所有数据库条目
    entries = get_all_database_entries()
    
    if entries is None:
        print("❌ 获取数据库条目失败")
        sys.exit(1)
    
    print(f"📊 总共找到 {len(entries)} 个条目")
    
    # 查找重复项
    duplicates = find_duplicates_advanced(entries)
    
    # 输出结果
    result = format_advanced_output(duplicates, len(entries))
    print(result)
    
    # 保存详细报告
    try:
        with open("notion_duplicates_detailed_report.txt", "w", encoding="utf-8") as f:
            f.write(result)
        print(f"📄 详细报告已保存到: notion_duplicates_detailed_report.txt")
        
        # 生成删除脚本
        if duplicates:
            deletion_script = generate_deletion_script(duplicates)
            with open("notion_deletion_script.sh", "w", encoding="utf-8") as f:
                f.write(deletion_script)
            print(f"🗑️  删除脚本已保存到: notion_deletion_script.sh (仅供参考)")
            
    except Exception as e:
        print(f"⚠️  保存文件时出错: {e}")

if __name__ == "__main__":
    main()
